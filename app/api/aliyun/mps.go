package aliyun

import (
	"vlab/app/api/aliyun/common"
	"vlab/app/api/aliyun/mps"
	"vlab/app/common/dbs"
	videoDao "vlab/app/dao/content_video"
	videoMpsDao "vlab/app/dao/content_video_mps"
	aliyunDto "vlab/app/dto/api/aliyun"
	aliyunJob "vlab/app/job/video/aliyun"
	showSrv "vlab/app/service/show"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// SubmitTranscodeJob .
func SubmitTranscodeJob(ctx *gin.Context) {
	req := aliyunDto.SubmitTranscodeJobReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	ctxAccount, _ := helper.GetCtxAccount(ctx)
	if ctxAccount.AccountID != dbs.RootUID {
		helper.AppResp(ctx, ecode.AccountNeedRootAuthErr.Code(), ecode.AccountNeedRootAuthErr.Message())
		return
	}

	videoMpsModel := &videoMpsDao.Model{}
	videoMpsModel.VideoID = req.VideoID
	videoMpsModel.VideoKey = req.VideoKey

	ret, err := mps.GetApi().SubmitTranscodeJob(ctx, videoMpsModel, common.ResolutionList)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// SubmitTranscodeJobChan .
func SubmitTranscodeJobChan(ctx *gin.Context) {
	req := aliyunDto.SubmitTranscodeJobChanReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	ctxAccount, _ := helper.GetCtxAccount(ctx)
	if ctxAccount.AccountID != dbs.RootUID {
		helper.AppResp(ctx, ecode.AccountNeedRootAuthErr.Code(), ecode.AccountNeedRootAuthErr.Message())
		return
	}

	video, err := videoDao.GetRepo().FetchByID(ctx, req.VideoID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	if video.ID == 0 || video.VideoPath == "" {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	if err := showSrv.GetVideoMpsEntity().Producer(ctx, video); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), video)
}

// GetTranscodeJobSignedUrl .
func GetTranscodeJobSignedUrl(ctx *gin.Context) {
	req := aliyunDto.GetTranscodeJobSignedUrl{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	ctxAccount, _ := helper.GetCtxAccount(ctx)
	if ctxAccount.AccountID != dbs.RootUID {
		helper.AppResp(ctx, ecode.AccountNeedRootAuthErr.Code(), ecode.AccountNeedRootAuthErr.Message())
		return
	}

	video, err := videoDao.GetRepo().FetchByID(ctx, req.VideoID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	if video.ID == 0 || video.VideoPath == "" {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, _, _, err := showSrv.GetService().GetVideoSignedUrl(ctx, video, common.Resolution(req.Resolution))
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// ResubmitTranscodeFailedJob .
func ResubmitTranscodeFailedJob(ctx *gin.Context) {
	// req := aliyunDto.SubmitTranscodeJobChanReq{}
	// if err := ctx.ShouldBindQuery(&req); err != nil {
	// 	helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
	// 	return
	// }
	ctxAccount, _ := helper.GetCtxAccount(ctx)
	if ctxAccount.AccountID != dbs.RootUID {
		helper.AppResp(ctx, ecode.AccountNeedRootAuthErr.Code(), ecode.AccountNeedRootAuthErr.Message())
		return
	}

	if err := aliyunJob.GetJob().ResubmitTranscodeFailedJob(); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), "success")
}
