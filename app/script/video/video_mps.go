package video

import (
	"context"
	"time"
	"vlab/app/api/bytes/vod"
	byteVodApi "vlab/app/api/bytes/vod"
	"vlab/app/common/dbs"
	adminConfig "vlab/app/dao/admin_config"
	video "vlab/app/dao/content_video"
	videoMpsDao "vlab/app/dao/content_video_mps"
	videoCpMpsDao "vlab/app/dao/content_video_mps/cp"
	"vlab/app/service/show"
	"vlab/pkg/helper"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"github.com/gogf/gf/v2/util/gconv"
)

// FlushVideoMapAliyunToBytes .
func (e *Entry) FlushVideoMapAliyunToBytes(ctx *gin.Context, videoStart, videoEnd uint64) error {
	helper.AsyncDo(func() {
		var (
			total, countNum int64
			successNum      int
			err             error
			newCtx          = ctx.Copy()
		)
		newCtx.Request = newCtx.Request.Clone(context.WithoutCancel(newCtx.Request.Context()))
		confVid := adminConfig.RedisGetConfig[uint64](newCtx, adminConfig.KeyBytesVodStartVideoID)
		filter := &videoMpsDao.Filter{
			NotOperateType: uint32(videoMpsDao.OtByteVodPull),
			Status:         uint32(videoMpsDao.MpsStatusSuccess),
			WaitMigrate:    dbs.True,
			GetVideoID:     videoStart,
			LetVideoID:     confVid,
		}

		if videoEnd < confVid {
			filter.LetVideoID = videoEnd
		}

		if total, err = e.VideoMpsRepo.CountByFilter(newCtx, filter); err != nil {
			log.Ctx(newCtx).WithError(err).Error("CountByFilter FlushVideoMapAliyunToBytesError")
			return
		}
		countNum = total
		filter.Limit = 100
		filter.Sort = dbs.CommonSort{
			Field:  dbs.SortFieldID,
			Method: dbs.SortMethodAsc,
		}

		for {
			if total <= 0 {
				break
			}
			tempList, err := e.VideoMpsRepo.FindByFilter(newCtx, filter)
			if err != nil {
				log.Ctx(newCtx).WithError(err).Error("FindByFilter FlushVideoMapAliyunToBytesError")
				break

			}
			tempLen := len(tempList)
			if tempLen == 0 {
				break
			}

			for _, val := range tempList {
				videoModel := &video.Video{}
				videoModel.ID = val.VideoID
				_, _, _, err := show.GetService().GetVideoSignedUrl(newCtx, videoModel, val.Resolution)
				if err != nil {
					log.Ctx(newCtx).WithError(err).Error("GetVideoSignedUrl FlushVideoMapAliyunToBytesError")
					continue
				}

				// GetVideoSignedUrl 中已进行上传，此处不再继续上传，此处注释掉
				// if err := show.GetService().UploadAliyunVideoUrlToBytes(newCtx, val, signedUrl); err != nil {
				// 	continue
				// }

				time.Sleep(time.Millisecond * 50)
				successNum += 1
			}

			filter.GtID = tempList[tempLen-1].ID
			total -= int64(tempLen)
		}
		log.Ctx(newCtx).WithField("successNum", successNum).WithField("countNum", countNum).
			WithField("videoStart", videoStart).WithField("videoEnd", videoEnd).
			Info("FlushVideoMapAliyunToBytesSuccess")
	})

	return nil
}

// FlushVideoMapAliyunToBytesRetry .
func (e *Entry) FlushVideoMapAliyunToBytesRetry(ctx *gin.Context, videoStart, videoEnd uint64) error {
	helper.AsyncDo(func() {
		var (
			total, countNum int64
			successNum      int
			err             error
			newCtx          = ctx.Copy()
		)
		newCtx.Request = newCtx.Request.Clone(context.WithoutCancel(newCtx.Request.Context()))
		filter := &videoCpMpsDao.Filter{
			Status:     uint32(videoCpMpsDao.MpsStatusFailed),
			GetVideoID: videoStart,
			LetVideoID: videoEnd,
		}

		if total, err = e.VideoCpMpsRepo.CountByFilter(newCtx, filter); err != nil {
			return
		}
		countNum = total
		filter.Sort = dbs.CommonSort{
			Field:  dbs.SortFieldID,
			Method: dbs.SortMethodAsc,
		}

		for {
			if total <= 0 {
				break
			}
			tempList, err := e.VideoCpMpsRepo.FindByFilter(newCtx, filter)
			if err != nil {
				log.Ctx(newCtx).WithError(err).Error("FindByFilter FlushVideoMapAliyunToBytesError")
				break
			}
			tempLen := len(tempList)
			if tempLen == 0 {
				break
			}

			for _, val := range tempList {
				videoModel := &video.Video{}
				videoModel.ID = val.VideoID
				signedUrl, _, _, err := show.GetService().GetVideoSignedUrl(newCtx, videoModel, val.Resolution)
				if err != nil {
					log.Ctx(newCtx).WithError(err).Error("GetVideoSignedUrl FlushVideoMapAliyunToBytesError")
					continue
				}

				uploadRet, err := vod.GetApi().UploadMediaByUrl(newCtx, []string{signedUrl})
				if err != nil {
					log.Ctx(newCtx).WithField("signedUrl", signedUrl).WithError(err).Error("UploadMediaByUrl FlushVideoMapAliyunToBytesError")
					continue
				}
				if len(uploadRet.GetData()) == 0 {
					log.Ctx(newCtx).WithField("signedUrl", signedUrl).Error("uploadRet.GetData()==0 FlushVideoMapAliyunToBytesError")
					continue
				}

				item := &videoCpMpsDao.Model{
					ID:              val.ID,
					VideoID:         val.VideoID,
					Resolution:      val.Resolution,
					TranscodeFormat: val.TranscodeFormat,
					Status:          videoCpMpsDao.MpsStatusIng,
					WaitMigrate:     dbs.True,
					VideoKey:        "",
					JobID:           uploadRet.GetData()[0].JobId,
					TempID:          "",
					Retry:           dbs.False,
					OperateType:     uint32(videoCpMpsDao.OtByteVodPull),
				}

				if err = e.VideoCpMpsRepo.CreateOrUpdate(newCtx, item); err != nil {
					break
				}

				time.Sleep(time.Second * 2)
				successNum += 1
			}

			total -= int64(tempLen)
		}
		log.Ctx(newCtx).WithField("successNum", successNum).WithField("countNum", countNum).
			WithField("videoStart", videoStart).WithField("videoEnd", videoEnd).
			Info("FlushVideoMapAliyunToBytesSuccess")
	})

	return nil
}

// FlushVideoMapBytesRuntime .
func (e *Entry) FlushVideoMapBytesRuntime(ctx *gin.Context, IdStart, IdEnd uint64) error {
	helper.AsyncDo(func() {
		var (
			total      int64
			successNum int
			err        error
			newCtx     = ctx.Copy()
		)
		newCtx.Request = newCtx.Request.Clone(context.WithoutCancel(newCtx.Request.Context()))

		filter := &videoMpsDao.Filter{
			OperateType: uint32(videoMpsDao.OtByteVodPull),
			Status:      uint32(videoMpsDao.MpsStatusSuccess),
			RuntimeType: dbs.True,
			GtID:        IdStart,
			LtID:        IdEnd,
		}

		if total, err = e.VideoMpsRepo.CountByFilter(newCtx, filter); err != nil {
			log.Ctx(newCtx).WithError(err).Error("CountByFilter FlushVideoMapBytesRuntimeError")
			return
		}
		filter.Limit = 1000
		filter.Sort = dbs.CommonSort{
			Field:  dbs.SortFieldID,
			Method: dbs.SortMethodAsc,
		}

		for {
			if total <= 0 {
				break
			}
			tempList, err := e.VideoMpsRepo.FindByFilter(newCtx, filter)
			if err != nil {
				log.Ctx(newCtx).WithError(err).Error("FindByFilter FlushVideoMapBytesRuntimeError")
				break
			}
			tempLen := len(tempList)
			if tempLen == 0 {
				break
			}

			for _, val := range tempList {
				ret, err := byteVodApi.GetApi().GetPlayInfo(newCtx, val.VideoKey, val.GetBytesVodDefinition(dbs.False))
				if err != nil {
					log.Ctx(newCtx).WithError(err).Error("GetPlayInfo FlushVideoMapBytesRuntimeError")
					continue
				}

				if err = e.VideoMpsRepo.UpdateMapByID(newCtx, val.ID, map[string]interface{}{
					"runtime": gconv.Int64(ret.Duration),
				}); err != nil {
					return
				}
			}

			time.Sleep(time.Millisecond * 100)
			successNum += tempLen

			filter.GtID = tempList[tempLen-1].ID
			total -= int64(tempLen)
		}
	})

	return nil
}

// FlushVideoMapBytesCp 暂时用不到
func (e *Entry) FlushVideoMapBytesCp(ctx *gin.Context, videoStart, videoEnd uint64) error {
	helper.AsyncDo(func() {
		var (
			total, countNum int64
			successNum      int
			err             error
			newCtx          = ctx.Copy()
		)
		newCtx.Request = newCtx.Request.Clone(context.WithoutCancel(newCtx.Request.Context()))

		filter := &videoMpsDao.Filter{
			OperateType: uint32(videoMpsDao.OtByteVodPull),
			WaitMigrate: dbs.True,
			GetVideoID:  videoStart,
			LetVideoID:  videoEnd,
		}

		if total, err = e.VideoMpsRepo.CountByFilter(newCtx, filter); err != nil {
			log.Ctx(newCtx).WithError(err).Error("CountByFilter FlushVideoMapBytesCpError")
			return
		}
		countNum = total
		filter.Limit = 1000
		filter.Sort = dbs.CommonSort{
			Field:  dbs.SortFieldID,
			Method: dbs.SortMethodAsc,
		}

		for {
			if total <= 0 {
				break
			}
			tempList, err := e.VideoMpsRepo.FindByFilter(newCtx, filter)
			if err != nil {
				log.Ctx(newCtx).WithError(err).Error("FindByFilter FlushVideoMapBytesCpError")
				break
			}
			tempLen := len(tempList)
			if tempLen == 0 {
				break
			}

			var (
				mpsIds     = tempList.GetIds()
				insertList = make([]*videoCpMpsDao.Model, 0)
			)

			for _, val := range tempList {
				item := &videoCpMpsDao.Model{
					VideoID:         val.VideoID,
					Resolution:      val.Resolution,
					TranscodeFormat: val.TranscodeFormat,
					Status:          videoCpMpsDao.MpsStatus(val.Status),
					VideoKey:        val.VideoKey,
					JobID:           val.JobID,
					TempID:          val.TempID,
					Retry:           val.Retry,
					OperateType:     uint32(videoCpMpsDao.OtByteVodPull),
				}
				insertList = append(insertList, item)
			}

			tx := dbs.NewMysqlEngines().UseWithGinCtx(newCtx, true).Begin()
			if err := func() (err error) {
				if err = e.VideoMpsRepo.UpdateMapByIdsWithTx(newCtx, tx, mpsIds, map[string]interface{}{
					"wait_migrate": dbs.StatusDisable,
				}); err != nil {
					return
				}

				if err = e.VideoCpMpsRepo.BatchCreateWithTx(newCtx, tx, insertList); err != nil {
					return
				}

				return tx.Commit().Error
			}(); err != nil {
				tx.Rollback()
				log.Ctx(newCtx).WithError(err).Error("txErr FlushVideoMapBytesCpError")
				continue
			}

			time.Sleep(time.Millisecond * 100)
			successNum += tempLen

			filter.GtID = tempList[tempLen-1].ID
			total -= int64(tempLen)
		}
		log.Ctx(newCtx).WithField("successNum", successNum).WithField("countNum", countNum).
			WithField("videoStart", videoStart).WithField("videoEnd", videoEnd).
			Info("FlushVideoMapBytesCpSuccess")

	})

	return nil
}
