package user

import (
	"strings"
	"time"

	"vlab/app/common/dbs"
	userDto "vlab/app/dto/user"
	"vlab/pkg/util/timeUtil"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

var (
	_Model = &Model{}
	_Auth  = &Auth{}
)

type EntityType uint32
type AuditUid uint64

const (
	EtUser    EntityType = 1 // 用户
	EtVisitor EntityType = 2 // 游客

	AuditUidSkybox AuditUid = 1     // skybox 审核版本uid
	AuditUidVibox  AuditUid = 20865 // vibox 审核版本uid
	AuditUidHitv   AuditUid = 20865 // hitv 审核版本uid
	AuditUidAhatv  AuditUid = 20865 // ahatv 审核版本uid
	AuditUidHeytv  AuditUid = 20865 // heytv 审核版本uid
)

type Model struct {
	ID            uint64    `gorm:"primarykey" json:"id"`
	UUID          string    `json:"uuid,omitempty"`
	Nickname      string    `json:"nickname,omitempty"`
	Mobile        string    `json:"mobile,omitempty"`
	Email         string    `json:"email,omitempty"`
	Password      string    `json:"-"` // 密码字段，不返回给前端
	Avatar        string    `json:"avatar,omitempty"`
	Status        uint32    `json:"status,omitempty"`
	ChannelID     uint64    `json:"channel_id,omitempty"`
	PlatformID    uint32    `json:"platform_id,omitempty"`
	VipTime       int64     `json:"vip_time,omitempty"`
	GracePeriod   int64     `json:"grace_period,omitempty"` // 宽限期，到期后用户将被禁用
	WatchAdTime   int64     `json:"watch_ad_time,omitempty"`
	LastLoginTime int64     `json:"last_login_time,omitempty"`
	IsDeleted     uint32    `json:"is_deleted,omitempty"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "user"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

func (m *Model) GetLastLoginTime(ctx *gin.Context) string {
	if m.LastLoginTime == 0 {
		return ""
	}
	return timeUtil.ToDateTimeStringByZone(ctx, m.LastLoginTime)
}

// func (m *Model) PlatformBitOr(platform uint32) uint32 {
// 	return m.Platform | platform
// }

// func (m *Model) PlatformBitAnd(platform uint32) uint32 {
// 	return m.Platform & platform
// }

func (m *Model) ToUserInfoResp(ctx *gin.Context) *userDto.AdminUserListItem {
	ret := &userDto.AdminUserListItem{
		ID:            m.ID,
		UUID:          m.UUID,
		Nickname:      m.Nickname,
		Avatar:        m.Avatar,
		Mobile:        m.Mobile,
		Email:         m.Email,
		Status:        m.Status,
		VipTime:       m.VipTime,
		GracePeriod:   m.GracePeriod,
		WatchAdTime:   m.WatchAdTime,
		LastLoginTime: m.GetLastLoginTime(ctx),
	}

	ret.Email = MaskEmail(m.Email, 2, 2)

	return ret
}

// MaskEmail 对邮箱地址做局部脱敏。
// keepHead 与 keepTail 分别表示要保留的前/后字符数（按“字符”而非字节计）。
func MaskEmail(addr string, keepHead, keepTail int) string {
	parts := strings.SplitN(addr, "@", 2)
	if len(parts) != 2 {
		return addr // 非邮箱
	}

	// ⚠️ 用 []rune 处理，防止多字节字符被截断
	localRunes := []rune(parts[0])
	l := len(localRunes)
	if l == 0 {
		return addr // 极端情况，本地段为空
	}

	// 负数参数视为 0
	if keepHead < 0 {
		keepHead = 0
	}
	if keepTail < 0 {
		keepTail = 0
	}

	// 至少保留 1 个头；不能越界
	if keepHead == 0 {
		keepHead = 1
	}
	if keepHead > l {
		keepHead = l // 全保留也没关系，后面 mask 会变 1 个 *
	}

	// 如果总保留长度 >= 本地段长度，优先裁掉尾巴
	if keepHead+keepTail >= l {
		keepTail = 0
	}

	head := string(localRunes[:keepHead])
	tail := ""
	if keepTail > 0 {
		tail = string(localRunes[l-keepTail:])
	}

	maskLen := l - keepHead - keepTail
	if maskLen < 1 {
		maskLen = 1
	}
	mask := strings.Repeat("*", maskLen)

	return head + mask + tail + "@" + parts[1]
}

type Filter struct {
	ID         uint64
	NotID      uint64
	EmptyUUID  bool
	IDS        []uint64
	UUID       string
	Nickname   string
	Mobile     string
	Email      string
	EmailLike  string
	PlatformID uint32
	Status     uint32
	Sort       dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetIDS() []uint64 {
	ret := lo.Map(ml, func(item *Model, idx int) uint64 {
		return item.ID
	})
	ret = lo.Uniq(ret)
	return ret
}

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}
