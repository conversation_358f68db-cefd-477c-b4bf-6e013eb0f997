package admin

import (
	"sync"
	"vlab/app/common/dbs"
	adminAccount "vlab/app/dao/admin_account"
	adminArea "vlab/app/dao/admin_area"
	adminMenu "vlab/app/dao/admin_menu"
	menuBackup "vlab/app/dao/admin_menu_backup"
	adminRole "vlab/app/dao/admin_role"
	adminDto "vlab/app/dto/admin"

	"github.com/gin-gonic/gin"
)

type Server interface {
	RoleSrv
	AccountSrv
	MenuSrv
	AreaSrv
}

type RoleSrv interface {
	CheckOperationRoleAuth(ctx *gin.Context, roleID uint64) error
	JudgeIsRoot(roleID uint64) bool
	JudgeIsAdmin(roleID uint64) bool
	EditableFirstAdmin(accountID uint64) bool
	GetCommonRoleFilter(ctx *gin.Context) []uint64
	GetCommonAccountFilter(ctx *gin.Context) []uint64
	AdminRoleList(ctx *gin.Context, req *adminDto.AdminRoleListReq) (*adminDto.AdminRoleListResp, error)
	AdminSetRole(ctx *gin.Context, req *adminDto.AdminSetRoleReq) (*adminRole.Model, error)
	AdminSetRoleAuth(ctx *gin.Context, roleID uint64, MenuIds []uint64) error
	AdminDelRole(ctx *gin.Context, roleID uint64) error
	AdminRoleAll(ctx *gin.Context, req *adminDto.AdminRoleAllReq) ([]*adminDto.AdminRoleListItem, error)
}

type AccountSrv interface {
	CheckOperationAccountAuth(ctx *gin.Context, accountID, roleID uint64) error
	AdminAccountList(ctx *gin.Context, req *adminDto.AdminAccountListReq) (*adminDto.AdminAccountListResp, error)
	AdminSetAccount(ctx *gin.Context, req *adminDto.AdminSetAccountReq) error
	AdminDelAccount(ctx *gin.Context, accountID uint64) error
	AdminSetAccountPwd(ctx *gin.Context, id uint64, pwd string) error
	AdminGetAccountInfo(ctx *gin.Context, aid, rid uint64) (*adminDto.AdminGetAccountInfoResp, error)
	AdminAccountAll(ctx *gin.Context, req *adminDto.AdminAccountAllReq) ([]*adminDto.AdminAccountAllItem, error)
	AdminAccountLogin(ctx *gin.Context, account, pwd string) (string, error)
}

type MenuSrv interface {
	AdminMenuList(ctx *gin.Context) ([]*adminDto.AdminMenuListResp, error)
	AdminMenuOption(ctx *gin.Context, req *adminDto.AdminMenuOptionReq) ([]*adminDto.AdminMenuOptionResp, error)
	AdminMenuRouter(ctx *gin.Context, roleId uint64) ([]*adminDto.AdminMenuRouterResp, error)
	AdminSetMenu(ctx *gin.Context, req *adminDto.AdminSetMenuReq) error
	AdminOperateMenu(ctx *gin.Context, id uint64, action dbs.OperateAction) error
	AdminMenuInit(ctx *gin.Context) error
}

type AreaSrv interface {
	AdminAreaList(ctx *gin.Context) ([]*adminDto.AdminAreaListResp, error)
}

type Entry struct {
	MenuRepo       adminMenu.Repo
	AreaRepo       adminArea.Repo
	AccountRepo    adminAccount.Repo
	RoleRepo       adminRole.Repo
	MenuBackupRepo menuBackup.Repo
}

var (
	defaultEntry         Server
	defaultEntryInitOnce sync.Once
)

func GetService() Server {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		RoleRepo:       adminRole.GetRepo(),
		AccountRepo:    adminAccount.GetRepo(),
		MenuRepo:       adminMenu.GetRepo(),
		MenuBackupRepo: menuBackup.GetRepo(),
		AreaRepo:       adminArea.GetRepo(),
	}
}
