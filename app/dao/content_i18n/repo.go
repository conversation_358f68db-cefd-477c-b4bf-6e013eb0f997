package i18n

import (
	"fmt"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"vlab/app/common/dbs"

	"github.com/gin-gonic/gin"
)

func (e Entry) FindByFilter(ctx *gin.Context, filter *Filter) (I18nList, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&I18n{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(
		filter.fullScopes()...,
	)

	ret := I18nList{}
	if err := query.Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FindByFilterWithFallback 带回退功能的查询方法
// 如果目标语言查询无结果，自动回退到 fallbackLang 指定的语言
func (e Entry) FindByFilterWithFallback(ctx *gin.Context, filter *Filter, fallbackLang string) (I18nList, error) {
	// 先查询目标语言
	result, err := e.FindByFilter(ctx, filter)
	if err != nil {
		return result, err
	}

	// 如果有结果或者目标语言就是回退语言，直接返回
	if len(result) > 0 || filter.ISO_639_1 == fallbackLang {
		return result, nil
	}

	// 如果没有结果且目标语言不是回退语言，尝试查询回退语言
	fallbackFilter := *filter
	fallbackFilter.ISO_639_1 = fallbackLang
	
	return e.FindByFilter(ctx, &fallbackFilter)
}

func (e Entry) CreateOrUpdate(context *gin.Context, I18n *I18n) (uint64, error) {
	return e.CreateOrUpdateWithTx(context, e.MysqlEngine.UseWithGinCtx(context, true), I18n)
}

func (e Entry) CreateOrUpdateWithTx(context *gin.Context, db *gorm.DB, I18n *I18n) (uint64, error) {
	panic("implement me")
}

func (e Entry) BatchCreateOrUpdate(context *gin.Context, I18nList []*I18n) error {
	return e.BatchCreateOrUpdateWithTx(context, e.MysqlEngine.UseWithGinCtx(context, true), I18nList)
}

func (e Entry) BatchCreateOrUpdateWithTx(context *gin.Context, db *gorm.DB, I18nList []*I18n) error {
	if len(I18nList) == 0 {
		return nil
	}
	if err := db.Model(&I18n{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns: []clause.Column{{Name: "key"}, {Name: "iso_639_1"}},
			DoUpdates: clause.AssignmentColumns([]string{
				"value",
			}),
		}).CreateInBatches(I18nList, 3000).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) Create(ctx *gin.Context, model *I18n) (uint64, error) {
	return e.CreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), model)
}

func (e Entry) CreateWithTx(ctx *gin.Context, db *gorm.DB, model *I18n) (uint64, error) {
	if err := db.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(&model).Error; err != nil {
		return 0, err
	}
	return model.ID, nil
}

func (e Entry) BatchCreate(ctx *gin.Context, models []*I18n) error {
	return e.BatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), models)
}

func (e Entry) BatchCreateWithTx(ctx *gin.Context, db *gorm.DB, models []*I18n) error {
	if len(models) == 0 {
		return nil
	}

	if err := db.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(models).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) UpdateModelByID(ctx *gin.Context, u uint64, model *I18n) error {
	return e.UpdateModelByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), u, model)
}

func (e Entry) UpdateModelByIDWithTx(ctx *gin.Context, db *gorm.DB, id uint64, model *I18n) error {
	if err := db.Model(&I18n{}).Where("id = ?", id).Updates(model).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) UpdateMapByID(ctx *gin.Context, u uint64, m map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), u, m)
}

func (e Entry) UpdateMapByIDWithTx(ctx *gin.Context, db *gorm.DB, id uint64, m map[string]interface{}) error {
	if err := db.Model(&I18n{}).Where("id = ?", id).Updates(m).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) DataPageList(ctx *gin.Context, filter *Filter, page int, limit int) (total int64, list I18nList, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&I18n{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query = query.Scopes(filter.fullScopes()...)

	query.Select("id").Count(&total)
	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).
		Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

func (e Entry) FindXidsByFilter(ctx *gin.Context, filter *Filter, field dbs.PluckField) ([]uint64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&I18n{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(filter.fullScopes()...)

	ret := []uint64{}
	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FetchByID(ctx *gin.Context, id uint64) (*I18n, error) {
	ret := &I18n{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&I18n{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FeatchByFilterSort(ctx *gin.Context, filter *Filter) (*I18n, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&I18n{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(filter.fullScopes()...)

	ret := &I18n{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) CountByFilter(ctx *gin.Context, filter *Filter) (int64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&I18n{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)) // 未删除

	query.Scopes(
		filter.fullScopes()...,
	)

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return 0, err
	}
	return total, nil
}

func (e Entry) UpdateMapByFilter(ctx *gin.Context, filter *Filter, m map[string]interface{}) error {
	return e.UpdateMapByFilterWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), filter, m)
}

func (e Entry) UpdateMapByFilterWithTx(ctx *gin.Context, db *gorm.DB, filter *Filter, m map[string]interface{}) error {
	query := db.Model(&I18n{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)) // 未删除

	query.Scopes(
		filter.fullScopes()...,
	)
	if err := query.Model(&I18n{}).Updates(m).Error; err != nil {
		return err
	}
	return nil
}
