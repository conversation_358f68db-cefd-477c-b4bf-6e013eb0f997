package data

import (
	"github.com/gin-gonic/gin"
	showDao "vlab/app/dao/content_show"
	dataDto "vlab/app/dto/data"
	"vlab/pkg/ecode"
	"vlab/pkg/util/timeUtil"
)

func (e *Entry) DataShowSearch(ctx *gin.Context, req *dataDto.DataShowSearchReq) (*dataDto.DataShowSearchResp, error) {

	if req.Limit <= 0 {
		req.Limit = 50
	}
	if req.QueryStart == 0 || req.QueryEnd == 0 {
		return nil, nil
	}
	if req.QueryStart > req.QueryEnd {
		return nil, ecode.ParamErr
	}

	filter := &showDao.SearchHistoryFilter{
		CreateStart: timeUtil.ToDateTimeStringByZone(ctx, req.QueryStart),
		CreateEnd:   timeUtil.ToDateTimeStringByZone(ctx, req.QueryEnd),
	}

	resp := &dataDto.DataShowSearchResp{
		List:  make([]*dataDto.DataShowSearchItem, 0),
		Count: 0,
	}

	req.Page = 1
	if req.Limit > 500 {
		req.Limit = 500
	}

	group, err := e.SearchHistoryRepo.SearchHistoryGroup(ctx, filter, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}
	if len(group) == 0 {
		return resp, nil
	}

	resp.Count = int64(len(group))

	for _, item := range group {
		resp.List = append(resp.List, &dataDto.DataShowSearchItem{
			Keyword: item.Keyword,
			Lang:    item.Lang,
			Num:     item.Count,
		})
	}
	return resp, nil
}
