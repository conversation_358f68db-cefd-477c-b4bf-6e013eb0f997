package show

import (
	"context"
	"sync"
	"time"
	"vlab/app/api/aliyun/common"
	ossApi "vlab/app/api/aliyun/oss"
	vodApi "vlab/app/api/aliyun/vod"
	"vlab/app/api/bytes/vod"
	byteVodApi "vlab/app/api/bytes/vod"
	"vlab/app/common/dbs"
	adminConfig "vlab/app/dao/admin_config"
	videoDao "vlab/app/dao/content_video"
	videoMpsDao "vlab/app/dao/content_video_mps"
	videoCpMpsDao "vlab/app/dao/content_video_mps/cp"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"
	"vlab/pkg/util/ctxUtil"

	"github.com/gin-gonic/gin"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/ua-parser/uap-go/uaparser"
)

// GetBytesVideoSignedUrl .
func (e *Entry) GetBytesVideoSignedUrl(ctx *gin.Context, video *videoDao.Video, resolution common.Resolution) (string, common.Resolution, int64, error) {
	var (
		videoMps      = &videoMpsDao.Model{}
		runtime       int64
		retResolution = resolution
		signedUrl     string
		err           error
		filter        = &videoMpsDao.Filter{
			VideoID:    video.ID,
			Resolution: resolution,
			Status:     uint32(videoMpsDao.MpsStatusSuccess),
		}
	)
	if video.ID == 0 {
		return signedUrl, retResolution, runtime, nil
	}

	if videoMps, err = e.VideoMpsRepo.FetchByFilter(ctx, filter); err != nil {
		return signedUrl, retResolution, runtime, err
	}
	if videoMps.VideoKey == "" {
		if videoMps, err = e.VideoMpsRepo.FeatchByFilterSort(ctx, &videoMpsDao.Filter{
			VideoID: video.ID,
			Status:  uint32(videoMpsDao.MpsStatusSuccess),
			Sort:    dbs.CommonSort{Field: videoMpsDao.SortFieldResolution, Method: dbs.SortMethodAsc},
		}); err != nil {
			return signedUrl, retResolution, runtime, err
		}
		if videoMps.VideoKey == "" {
			return signedUrl, retResolution, runtime, nil
		}
	}
	retResolution = videoMps.Resolution

	ret, err := byteVodApi.GetApi().GetPlayInfo(ctx, videoMps.VideoKey, videoMps.GetBytesVodDefinition(dbs.False))
	if err != nil {
		return signedUrl, retResolution, runtime, err
	}
	if len(ret.PlayInfoList) > 0 {
		signedUrl = ret.GetPlayInfoList()[0].GetMainPlayUrl()
		runtime = gconv.Int64(ret.Duration)
	}

	return signedUrl, retResolution, runtime, nil
}

// GetVideoSignedUrl 迁移至字节后废弃
func (e *Entry) GetVideoSignedUrl(ctx *gin.Context, video *videoDao.Video, resolution common.Resolution) (string, common.Resolution, int64, error) {
	var (
		videoMps      = &videoMpsDao.Model{}
		runtime       int64
		bucket        = common.BucketTypeVideoMps
		retResolution = resolution
		signedUrl     string
		num           int64
		err           error
		filter        = &videoMpsDao.Filter{
			VideoID:    video.ID,
			Resolution: resolution,
			Status:     uint32(videoMpsDao.MpsStatusSuccess),
		}
	)
	if video.ID == 0 {
		return signedUrl, retResolution, runtime, nil
	}

	if num, err = e.VideoMpsRepo.CountByFilter(ctx, &videoMpsDao.Filter{
		VideoID:        video.ID,
		OperateType:    uint32(videoMpsDao.OtVodUpload),
		ResolutionZero: true,
	}); err != nil {
		return signedUrl, retResolution, runtime, err
	}
	if num == dbs.True {
		filter.Resolution = dbs.False
		filter.ResolutionZero = true
	}

	if videoMps, err = e.VideoMpsRepo.FetchByFilter(ctx, filter); err != nil {
		return signedUrl, retResolution, runtime, err
	}
	if videoMps.MpsVideoKey == "" {
		if videoMps, err = e.VideoMpsRepo.FeatchByFilterSort(ctx, &videoMpsDao.Filter{
			VideoID: video.ID,
			Status:  uint32(videoMpsDao.MpsStatusSuccess),
			Sort:    dbs.CommonSort{Field: videoMpsDao.SortFieldResolution, Method: dbs.SortMethodAsc},
		}); err != nil {
			return signedUrl, retResolution, runtime, err
		}
		if videoMps.MpsVideoKey == "" {
			return signedUrl, retResolution, runtime, nil
		}
	}
	retResolution = videoMps.Resolution
	if videoMps.OperateType == uint32(videoMpsDao.OtByteVodPull) {
		return e.GetBytesVideoSignedUrl(ctx, video, resolution)
	}

	switch videoMps.OperateType {
	case uint32(videoMpsDao.OtVodUpload):
		_, ret, err := vodApi.GetApi().GetVideoPlayInfo(ctx, videoMps.MpsVideoKey, videoMps.GetVodDefinition(resolution))
		if err != nil {
			return signedUrl, retResolution, runtime, err
		}
		signedUrl, runtime = ret.PlayURL, gconv.Int64(ret.Duration)
	case uint32(videoMpsDao.OtVodPull):
		ret, err := vodApi.GetApi().GetMezzanineInfo(ctx, videoMps.MpsVideoKey)
		if err != nil {
			return signedUrl, retResolution, runtime, err
		}
		signedUrl, runtime = ret.FileURL, gconv.Int64(ret.Duration)
	case uint32(videoMpsDao.OtOssUpload):
		ret, err := ossApi.GetApi().GenVideoSignedUrl(ctx, bucket, videoMps.MpsVideoKey)
		if err != nil {
			return signedUrl, retResolution, runtime, err
		}
		signedUrl = ret.SignedUrl
	}
	if videoMps.WaitMigrate == dbs.True {
		e.UploadAliyunVideoUrlToBytes(ctx, videoMps, signedUrl)
	}

	return signedUrl, retResolution, runtime, nil
}

// UploadAliyunVideoUrlToBytes .
func (e *Entry) UploadAliyunVideoUrlToBytes(ctx *gin.Context, videoMps *videoMpsDao.Model, signedUrl string) error {
	if signedUrl == "" {
		return nil
	}

	uploadRet, err := vod.GetApi().UploadMediaByUrl(ctx, []string{signedUrl})
	if err != nil {
		log.Ctx(ctx).WithField("signedUrl", signedUrl).WithError(err).Error("UploadMediaByUrl UploadAliyunVideoUrlToBytes")
		return err
	}
	if len(uploadRet.GetData()) == 0 {
		log.Ctx(ctx).WithField("signedUrl", signedUrl).Error("uploadRet.GetData()==0 UploadAliyunVideoUrlToBytes")
		return err
	}

	cpMps := &videoCpMpsDao.Model{
		VideoID:         videoMps.VideoID,
		Resolution:      videoMps.Resolution,
		TranscodeFormat: videoMps.TranscodeFormat,
		Status:          videoCpMpsDao.MpsStatusIng,
		WaitMigrate:     dbs.True,
		VideoKey:        "",
		JobID:           uploadRet.GetData()[0].JobId,
		TempID:          "",
		Retry:           dbs.False,
		OperateType:     uint32(videoCpMpsDao.OtByteVodPull),
	}

	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	if err := func() (err error) {
		if err = e.VideoMpsRepo.UpdateMapByIDWithTx(ctx, tx, videoMps.ID, map[string]interface{}{
			"wait_migrate": dbs.StatusDisable,
		}); err != nil {
			return
		}

		if err = e.VideoCpMpsRepo.CreateWithTx(ctx, tx, cpMps); err != nil {
			return
		}

		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("txErr UploadAliyunVideoUrlToBytes")
		return err
	}
	return nil
}

var (
	videoMpsInit   sync.Once
	videoMpsEntity *VideoMpsEntity
	logBatchSize   = 100
	logTickerTime  = time.Second
)

type VideoMpsEntity struct {
	transChan chan *transChan
	pullChan  chan *pullChan

	done     chan bool
	mu       sync.Mutex
	isClosed bool
	parser   *uaparser.Parser
}

type transChan struct {
	videoMpsModel *videoMpsDao.Model
	ctx           *gin.Context
}

type pullChan struct {
	videoModel *videoDao.Video
	urlList    []*UploadMediaByUrlItem
	ctx        *gin.Context
}

type UploadMediaByUrlItem struct {
	Resolution common.Resolution `json:"resolution"`
	Url        string            `json:"url"`
}

// GetVideoMpsEntity
func GetVideoMpsEntity() *VideoMpsEntity {
	if videoMpsEntity == nil {
		videoMpsInit.Do(func() {
			videoMpsEntity = newVideoMpsEntity()
			newCtx := ctxUtil.NewRequestID(context.Background())
			videoMpsEntity.Start(newCtx)
			helper.GetAppSrvMgr(newCtx).Register(videoMpsEntity)
		})
	}
	return videoMpsEntity
}

func newVideoMpsEntity() *VideoMpsEntity {
	return &VideoMpsEntity{
		transChan: make(chan *transChan, 1000),
		pullChan:  make(chan *pullChan, 1000),
		done:      make(chan bool),
		parser:    uaparser.NewFromSaved(),
	}
}

func (l *VideoMpsEntity) Start(ctx context.Context) error {
	helper.AsyncDo(func() {
		var (
			ticker  = time.NewTicker(logTickerTime)
			msgList = make(videoMpsDao.ModelList, 0, logBatchSize)
			// transcodeFromat common.TranscodingFormat
			ginC *gin.Context
		)
		for {
			select {
			// 20250527 迁移至字节，不再支持阿里云转码功能，后续待定
			// case msg := <-l.transChan:
			// 	log.Ctx(msg.ctx).WithField("ChanMsg", msg).Info("transChanMsg")
			// 	ginC = msg.ctx
			// 	if transcodeFromat == dbs.False {
			// 		transcodeFromat = common.TranscodingFormat(adminConfig.RedisGetConfig[uint32](ginC, adminConfig.KeyVideoTranscodeFormat))
			// 	}
			// 	msgList = append(msgList, &videoMpsDao.Model{
			// 		VideoID:         msg.videoMpsModel.VideoID,
			// 		Resolution:      dbs.False,
			// 		TranscodeFormat: transcodeFromat,
			// 		Status:          videoMpsDao.MpsStatusIng,
			// 		VideoKey:        msg.videoMpsModel.VideoKey,
			// 		MpsVideoKey:     msg.videoMpsModel.MpsVideoKey,
			// 		JobID:           "",
			// 		TempID:          "",
			// 		Retry:           1,
			// 		OperateType:     uint32(videoMpsDao.OtVodUpload),
			// 	})

			// 	if len(msgList) >= logBatchSize {
			// 		if err := videoMpsDao.GetRepo().BatchCreateOrUpdate(ginC, msgList); err != nil {
			// 			log.Ctx(msg.ctx).WithField("msg.videoMpsModel", msg.videoMpsModel).WithError(err).Error("VideoMpsEntity l.transChan error")
			// 			// TODO 告警
			// 		}
			// 		msgList = msgList[:0]
			// 		ticker.Reset(logTickerTime)
			// 	}
			case msg := <-l.pullChan:
				log.Ctx(msg.ctx).WithField("pullChan", msg).Info("pullChanMsg")
				if err := l.UploadMediaByUrl(msg.ctx, msg.videoModel, msg.urlList); err != nil {
					log.Ctx(msg.ctx).WithError(err).WithField("msg.videoModel", msg.videoModel).WithField("urlList", msg.urlList).WithError(err).Error("l.UploadMediaByUrl error")
					// TODO 告警
				}
			case <-ticker.C:
				if len(msgList) > 0 {
					if err := videoMpsDao.GetRepo().BatchCreateOrUpdate(ginC, msgList); err != nil {
						log.Ctx(ginC).WithError(err).Error("VideoMpsEntity ticker.C error ")
						// TODO 告警
					}
					msgList = msgList[:0]
				}
			case <-ctx.Done():
				l.mu.Lock()
				defer l.mu.Unlock()
				l.isClosed = true
				close(l.transChan)
				close(l.pullChan)
				l.done <- true
				return
			default:
				time.Sleep(200 * time.Millisecond)
			}
		}
	})
	return nil
}

func (l *VideoMpsEntity) Close(ctx context.Context) error {
	select {
	case <-l.done:
		// fmt.Println(l.Name(), " closed")
	case <-time.After(1 * time.Second):
		// fmt.Println(l.Name(), " close Waiting")
	}
	return nil
}

// Producer 转码任务
func (l *VideoMpsEntity) Producer(ctx *gin.Context, videoModel *videoDao.Video) error {
	l.mu.Lock()
	defer l.mu.Unlock()
	if l.isClosed {
		return nil
	}
	// 20250527 迁移至字节，不再支持阿里云转码功能，后续待定
	// if videoModel.ID == 0 || videoModel.VideoPath == "" {
	// 	return nil
	// }

	// mpsNum, err := videoMpsDao.GetRepo().CountByFilter(ctx, &videoMpsDao.Filter{
	// 	VideoID:  videoModel.ID,
	// 	VideoKey: videoModel.VideoPath,
	// })
	// if err != nil {
	// 	return err
	// }
	// if mpsNum > 0 {
	// 	return nil
	// }

	// // 注意是否泄漏
	// newCtx := ctx.Copy()
	// newCtx.Request = newCtx.Request.Clone(context.WithoutCancel(ctx.Request.Context()))

	// l.transChan <- &transChan{
	// 	videoMpsModel: &videoMpsDao.Model{
	// 		VideoID:     videoModel.ID,
	// 		VideoKey:    videoModel.VideoPath,
	// 		MpsVideoKey: videoModel.VideoPath,
	// 	},
	// 	ctx: newCtx,
	// }
	return nil
}

// ProducerPullUrl 拉取任务
func (l *VideoMpsEntity) ProducerPullUrl(ctx *gin.Context, videoModel *videoDao.Video, urlList []*UploadMediaByUrlItem) error {
	l.mu.Lock()
	defer l.mu.Unlock()
	if l.isClosed {
		return nil
	}
	if videoModel.ID == 0 {
		return nil
	}
	if videoModel.PathType == 1 && len(urlList) == 0 {
		return ecode.VodUploadMediaByUrlsErr
	}

	// 注意是否泄漏
	newCtx := ctx.Copy()
	newCtx.Request = newCtx.Request.Clone(context.WithoutCancel(ctx.Request.Context()))

	l.pullChan <- &pullChan{
		videoModel: videoModel,
		urlList:    urlList,
		ctx:        newCtx,
	}
	return nil
}

func (l *VideoMpsEntity) Name() string {
	return "Video Transcode"
}

func (l *VideoMpsEntity) UploadMediaByUrl(ctx *gin.Context, videoModel *videoDao.Video, urlList []*UploadMediaByUrlItem) error {
	var (
		mpsList         = videoMpsDao.ModelList{}
		uploadUrls      = []string{}
		uploadUrlMap    = map[string]struct{}{}
		jobMap          = map[string]string{}
		transcodeFromat = common.TranscodingFormat(adminConfig.RedisGetConfig[uint32](ctx, adminConfig.KeyVideoTranscodeFormat))
		err             error
	)

	for _, urlInfo := range urlList {
		if _, ok := uploadUrlMap[urlInfo.Url]; !ok {
			uploadUrls = append(uploadUrls, urlInfo.Url)
			uploadUrlMap[urlInfo.Url] = struct{}{}
		}
	}
	if len(uploadUrls) == dbs.False {
		return nil
	}

	uploadResp, err := byteVodApi.GetApi().UploadMediaByUrl(ctx, uploadUrls)
	if err != nil {
		return err
	}
	if len(uploadResp.GetData()) > 0 {
		for _, resp := range uploadResp.GetData() {
			jobMap[resp.SourceUrl] = resp.JobId
		}
	} else {
		log.Ctx(ctx).Error("byteVodApi.GetApi().UploadMediaByUrl uploadResp.GetData()==0")
	}

	for _, urlInfo := range urlList {
		_, ok := uploadUrlMap[urlInfo.Url]
		if !ok {
			continue
		}
		item := &videoMpsDao.Model{
			VideoID:         videoModel.ID,
			Resolution:      urlInfo.Resolution,
			TranscodeFormat: transcodeFromat,
			Status:          videoMpsDao.MpsStatusSrvError,
			VideoKey:        "",
			MpsVideoKey:     "",
			JobID:           "",
			TempID:          "",
			Retry:           1,
			OperateType:     uint32(videoMpsDao.OtByteVodPull),
		}
		if jobId, okk := jobMap[urlInfo.Url]; okk {
			item.JobID = jobId
			item.Status = videoMpsDao.MpsStatusIng
		}

		mpsList = append(mpsList, item)
	}

	if len(mpsList) > dbs.False {
		if err = videoMpsDao.GetRepo().BatchCreateOrUpdate(ctx, mpsList); err != nil {
			return err
		}
	}

	return nil
}
