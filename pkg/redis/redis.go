package redis

import (
	"context"
	"fmt"
	"sync"
	"time"
	"vlab/config"
	"vlab/pkg/log"

	"github.com/cenkalti/backoff/v4"
	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v8"
)

var (
	LockMaxInterval      = 10 * time.Second
	DefaultLockTime      = 15 * time.Second
	LockTimeTwentySecond = 20 * time.Second
	LockTimeHalfMinute   = 30 * time.Second
	LockTimeOneMinute    = 1 * time.Minute
	LockTimeTwoMinute    = 2 * time.Minute
)

type RedisConfig struct {
	Addr     string
	Password string
	Db       int
}

type RedisClient struct {
	*redis.Client
	mu         sync.Mutex
	redsyncMap map[string]*redsync.Mutex
}

var (
	defaultEntry         *RedisClient
	defaultEntryInitOnce sync.Once
)

// GetRedisClient .
func GetRedisClient() *RedisClient {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newRedis(config.RedisCfg)
		})
	}
	return defaultEntry

}

func newRedis(c *config.Redis) *RedisClient {
	client := redis.NewClient(&redis.Options{
		Addr:         c.Host,
		Username:     c.Username,
		Password:     c.Password,
		DB:           c.Db,
		PoolSize:     50,               // 连接池大小
		MinIdleConns: 5,                // 最小空闲连接数
		IdleTimeout:  30 * time.Second, // 空闲连接的超时时间
	})
	if _, err := client.Ping(context.Background()).Result(); err != nil {
		panic(fmt.Sprintf("redis connect err %s", err.Error()))
	}

	return &RedisClient{Client: client, redsyncMap: map[string]*redsync.Mutex{}}
}

// Lock .
func (c *RedisClient) Lock(ginC *gin.Context, key string, expiry time.Duration) error {
	ctx, cancel := context.WithTimeout(ginC.Request.Context(), expiry)
	defer cancel()

	b := backoff.NewExponentialBackOff()
	b.MaxInterval = LockMaxInterval
	if err := backoff.Retry(func() error {
		mutex := redsync.New(goredis.NewPool(c.Client)).NewMutex(key, redsync.WithExpiry(expiry))
		if err := mutex.Lock(); err != nil {
			return err
		}
		c.mu.Lock()
		defer c.mu.Unlock()
		c.redsyncMap[key] = mutex
		return nil
	}, backoff.WithContext(b, ctx)); err != nil {
		return err
	}
	return nil
}

// Unlock .
func (c *RedisClient) Unlock(ctx *gin.Context, key string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	if mutex, ok := c.redsyncMap[key]; ok {
		if _, err := mutex.Unlock(); err != nil {
			log.Ctx(ctx).WithError(err).WithField("key", key).Error("redsync unlock err")
		}
		delete(c.redsyncMap, key)
	}
}

func (c *RedisClient) DoHSet(ctx context.Context, key string, data map[string]interface{}, timeout int) (err error) {
	var list = make([]interface{}, 0)

	for k, v := range data {
		list = append(list, k, v)
	}

	if err = c.HSet(ctx, key, list...).Err(); err != nil {
		return err
	}

	if timeout > 0 {
		if err = c.Expire(ctx, key, time.Duration(timeout)).Err(); err != nil {
			return err
		}
	}

	return nil
}

func (c *RedisClient) DoSAdd(ctx context.Context, key string, data interface{}) (err error) {
	if err = c.SAdd(ctx, key, data).Err(); err != nil {
		return err
	}
	return nil
}
