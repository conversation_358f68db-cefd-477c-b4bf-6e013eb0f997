package user

import (
	"vlab/app/common/dbs"
	userDto "vlab/app/dto/user"
	"vlab/app/service/user"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminRoleList 列表
func AdminUserList(ctx *gin.Context) {
	req := userDto.AdminUserListReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	if req.Page <= 0 {
		req.Page = dbs.DefaultPage
	}
	if req.Limit <= 0 {
		req.Limit = dbs.DefaultLimit
	}

	ret, err := user.GetService().AdminUserList(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminOperateUser .
func AdminOperateUser(ctx *gin.Context) {
	req := userDto.AdminOperateUserReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}
	if err := user.GetService().AdminOperateUser(ctx, req.ID, dbs.OperateAction(req.Action), req.Value); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// AdminUserInfo 获取用户信息
func AdminUserInfo(ctx *gin.Context) {
	req := userDto.AdminUserInfoReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := user.GetService().AdminUserInfo(ctx, req.ID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminSetUser 设置
// func AdminSetUser(ctx *gin.Context) {
// 	req := userDto.AdminSetUserReq{}
// 	if err := ctx.ShouldBind(&req); err != nil {
// 		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
// 		return
// 	}

// 	if err := user.GetService().AdminSetUser(ctx, req); err != nil {
// 		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
// 		return
// 	}
// 	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
// }

// AdminSetUserPwd 设置密码
// func AdminSetUserPwd(ctx *gin.Context) {
// 	req := &userDto.AdminSetUserPwdReq{}
// 	if err := ctx.ShouldBind(req); err != nil {
// 		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
// 		return
// 	}
// 	if err := user.GetService().AdminSetUserPwd(ctx, req.ID, req.Pwd); err != nil {
// 		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
// 		return
// 	}

// 	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
// }

// // AdminUserSearch
// func AdminUserSearch(ctx *gin.Context) {
// 	req := &userDto.AdminUserSearchReq{}
// 	if err := ctx.ShouldBind(req); err != nil {
// 		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
// 		return
// 	}

// 	ret, err := user.GetService().AdminUserSearch(ctx, req)
// 	if err != nil {
// 		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
// 		return
// 	}

// 	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
// }
