package show

import (
	"context"
	"fmt"
	"sync"
	"time"
	userDao "vlab/app/dao/user"
	deviceDao "vlab/app/dao/user/device"
	watchVideoLog "vlab/app/dao/user/watch_video_log"
	watchVideoPv "vlab/app/dao/user/watch_video_pv"
	"vlab/pkg/helper"
	"vlab/pkg/util/ctxUtil"
	"vlab/pkg/util/timeUtil"

	"github.com/gin-gonic/gin"
	"github.com/ua-parser/uap-go/uaparser"
)

var (
	watchVideoInit       sync.Once
	watchVideoEntity     *WatchVideoEntity
	watchVideoSize       = 1000
	watchVideoTickerTime = time.Second

	watchVideoPvSize       = 5000
	watchVideoPvTickerTime = time.Second * 10
)

type WatchVideoEntity struct {
	watchVideoChan   chan *watchVideoChan
	watchVideoPvChan chan *watchVideoPvChan
	done             chan bool
	mu               sync.Mutex
	isClosed         bool
	parser           *uaparser.Parser
}

type watchVideoChan struct {
	logModel *watchVideoLog.Model
	ctx      *gin.Context
}

type watchVideoPvChan struct {
	pvModel *watchVideoPv.Model
	ctx     *gin.Context
}

// GetWatchVideoEntity
func GetWatchVideoEntity() *WatchVideoEntity {
	if watchVideoEntity == nil {
		watchVideoInit.Do(func() {
			watchVideoEntity = newWatchVideoEntity()
			newCtx := ctxUtil.NewRequestID(context.Background())
			watchVideoEntity.Start(newCtx)
			helper.GetAppSrvMgr(newCtx).Register(watchVideoEntity)
		})
	}
	return watchVideoEntity
}

func newWatchVideoEntity() *WatchVideoEntity {
	return &WatchVideoEntity{
		watchVideoChan:   make(chan *watchVideoChan, 2000),
		watchVideoPvChan: make(chan *watchVideoPvChan, 5000),
		done:             make(chan bool),
		parser:           uaparser.NewFromSaved(),
	}
}

func (l *WatchVideoEntity) Start(ctx context.Context) error {
	helper.AsyncDo(func() {
		var (
			watchVideoRepo = watchVideoLog.GetRepo()
			ticker         = time.NewTicker(watchVideoTickerTime)
			logList        = make(watchVideoLog.ModelList, 0, watchVideoSize)

			watchVideoPvRepo = watchVideoPv.GetRepo()
			pvTicker         = time.NewTicker(watchVideoPvTickerTime)
			pvList           = make(watchVideoPv.ModelList, 0, watchVideoPvSize)
			pvMap            = map[string]int{}
			pvMapIdx         int
			ginC             *gin.Context
		)
		for {
			select {
			case msg := <-l.watchVideoChan:
				videoLog := msg.logModel
				ginC = msg.ctx
				exist := watchVideoRepo.RedisVerifyWatchVideo(ginC, videoLog.ShowID, videoLog.EpisodeID, videoLog.EntityID, userDao.EntityType(videoLog.EntityType))
				if !exist {
					logList = append(logList, videoLog)
					if len(logList) >= watchVideoSize {
						watchVideoRepo.BatchCreate(ginC, logList)
						logList = logList[:0]
						ticker.Reset(watchVideoTickerTime)
					}
				}
			case <-ticker.C:
				if len(logList) > 0 {
					watchVideoRepo.BatchCreate(ginC, logList)
					logList = logList[:0]
				}

			case pvMsg := <-l.watchVideoPvChan:
				videoPv := pvMsg.pvModel
				ginC = pvMsg.ctx
				key := fmt.Sprintf("%v_%v_%v_%v", videoPv.ShowID, videoPv.EpisodeID, videoPv.EntityID, userDao.EntityType(videoPv.EntityType))
				if idx, ok := pvMap[key]; ok {
					pvList[idx].Num++
				} else {
					pvList = append(pvList, videoPv)
					pvMap[key] = pvMapIdx
					pvMapIdx++
				}

				if len(pvList) >= watchVideoPvSize {
					watchVideoPvRepo.BatchCreate(ginC, pvList)
					pvList = pvList[:0]
					pvMapIdx = 0
					pvMap = map[string]int{}
					ticker.Reset(watchVideoPvTickerTime)
				}
			case <-pvTicker.C:
				if len(pvList) > 0 {
					watchVideoPvRepo.BatchCreate(ginC, pvList)
					pvList = pvList[:0]
					pvMapIdx = 0
					pvMap = map[string]int{}
				}
			case <-ctx.Done():
				l.mu.Lock()
				defer l.mu.Unlock()
				l.isClosed = true
				close(l.watchVideoChan)
				close(l.watchVideoPvChan)
				l.done <- true
				ticker.Stop()
				pvTicker.Stop()
				return
			default:
				time.Sleep(200 * time.Millisecond)
			}
		}
	})

	return nil
}

func (l *WatchVideoEntity) Close(ctx context.Context) error {
	select {
	case <-l.done:
		// fmt.Println(l.Name(), " closed")
	case <-time.After(1 * time.Second):
		// fmt.Println(l.Name(), " close Waiting")
	}
	return nil
}

func (l *WatchVideoEntity) Producer(ctx *gin.Context, showID, episodeID uint64) error {
	l.mu.Lock()
	defer l.mu.Unlock()
	if l.isClosed {
		return nil
	}

	var (
		ctxUser, _    = helper.GetCtxUser(ctx)
		clientType, _ = helper.GetClientType(ctx)
		versisonID, _ = helper.GetCtxVersionID(ctx)
		channelID, _  = helper.GetCtxChannelID(ctx)
		deviceID, _   = helper.GetDeviceNo(ctx)
		headers, _    = json.Marshal(ctx.Request.Header)
		logModel      = &watchVideoLog.Model{
			EntityID:   ctxUser.UID,
			EntityType: uint32(userDao.EtUser),
			ShowID:     showID,
			EpisodeID:  episodeID,
			Date:       timeUtil.NowToDateStringByZone(ctx),
			ClientType: uint32(clientType),
			VersionID:  versisonID,
			ChannelID:  channelID,
			TraceID:    helper.GetGinRequestID(ctx),
			Header:     string(headers),
		}
		pvModel = &watchVideoPv.Model{
			EntityID:   ctxUser.UID,
			EntityType: uint32(userDao.EtUser),
			ShowID:     showID,
			EpisodeID:  episodeID,
			Num:        1,
			Date:       timeUtil.NowToDateStringByZone(ctx),
			Lang:       ctx.GetHeader("X-Language"),
			ClientType: uint32(clientType),
			VersionID:  versisonID,
			ChannelID:  channelID,
			TraceID:    helper.GetGinRequestID(ctx),
			Header:     string(headers),
		}
	)
	if ctxUser.UID == 0 {
		dInfo, err := deviceDao.GetRepo().RedisDeviceInfo(ctx, deviceID)
		if err != nil {
			return err
		}
		logModel.EntityID = dInfo.ID
		logModel.EntityType = uint32(userDao.EtVisitor)

		pvModel.EntityID = dInfo.ID
		pvModel.EntityType = uint32(userDao.EtVisitor)
	}

	if logModel.EntityID == 0 || pvModel.EntityID == 0 {
		return nil
	}

	c := ctx.Copy()
	c.Request = c.Request.Clone(context.WithoutCancel(c.Request.Context()))

	l.watchVideoChan <- &watchVideoChan{
		logModel: logModel,
		ctx:      c,
	}

	l.watchVideoPvChan <- &watchVideoPvChan{
		pvModel: pvModel,
		ctx:     c,
	}
	return nil
}

func (l *WatchVideoEntity) Name() string {
	return "WatchVideoLog"
}
