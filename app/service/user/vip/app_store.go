package vip

import (
	"vlab/app/common/dbs"
	"vlab/app/dao/common"
	"vlab/app/dao/user"
	vipBill "vlab/app/dao/user/vip_bill"
	payLog "vlab/app/dao/vip/pay_log"
	productDao "vlab/app/dao/vip/product"
	userDto "vlab/app/dto/user"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"
	"vlab/pkg/redis"
	"vlab/pkg/util/timeUtil"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/apple"
	"github.com/sirupsen/logrus"
)

type (
	AppleNotifyDecodeRest struct {
		RenewalInfo *apple.RenewalInfo     `json:"renewalInfo"`
		TransInfo   *apple.TransactionInfo `json:"transInfo"`
	}
)

const (
	// 通知类型常量
	// https://blog.csdn.net/zou8944/article/details/124800989
	// https://developer.apple.com/documentation/appstoreservernotifications/notificationtype
	NotificationTypeV2ConsumptionRequest     = "CONSUMPTION_REQUEST"       // 用户发起了 购买或自动续订 的退款请求，subType：ACCEPTED
	NotificationTypeV2DidChangeRenewalPref   = "DID_CHANGE_RENEWAL_PREF"   // 表示客户对其订阅计划进行了更改：subType：UPGRADE 或 DOWNGRADE 或 空(不修改)
	NotificationTypeV2DidChangeRenewalStatus = "DID_CHANGE_RENEWAL_STATUS" // 修改自动订阅状态，subType：AUTO_RENEW_ENABLED 或 AUTO_RENEW_DISABLED
	NotificationTypeV2DidFailToRenew         = "DID_FAIL_TO_RENEW"         // 订阅未能续订, subType: GRACE_PERIOD 在宽限期内继续提供服务, 空表示不在宽限期内，可停止服务
	NotificationTypeV2DidRenew               = "DID_RENEW"                 // 订阅已成功续订, subType：BILLING_RECOVERY 开始付费失败, 后来订阅恢复了
	NotificationTypeV2Expired                = "EXPIRED"                   // 订阅已过期, subType: VOLUNTARY / BILLING_RETRY / PRICE_INCREASE
	NotificationTypeV2GracePeriodExpired     = "GRACE_PERIOD_EXPIRED"      // 宽限期已过, 未续订订阅
	NotificationTypeV2OfferRedeemed          = "OFFER_REDEEMED"            // 客户兑换了订阅优惠, subType：UPGRADE 或 DOWNGRADE
	NotificationTypeV2PriceIncrease          = "PRICE_INCREASE"            // 系统已通知客户自动续订订阅价格上涨, subType: PENDING 还没同意 ACCEPTED 已同意
	NotificationTypeV2Refund                 = "REFUND"                    // 成功退款
	NotificationTypeV2RefundDeclined         = "REFUND_DECLINED"           // 拒绝退款
	NotificationTypeV2RenewalExtended        = "RENEWAL_EXTENDED"          // 延长订阅的续订日期
	NotificationTypeV2Revoke                 = "REVOKE"                    // 订阅购买者撤销了家庭共享
	NotificationTypeV2Subscribed             = "SUBSCRIBED"                // 已订阅自动续订订阅, subType: INITIAL_BUY 初次购买, RESUBSCRIBE 重新订阅
	NotificationTypeOneTimeCharge            = "ONE_TIME_CHARGE"           // 一次性收费 - SUBSCRIBED

	// 子类型常量
	// https://developer.apple.com/documentation/appstoreservernotifications/subtype
	SubTypeV2InitialBuy        = "INITIAL_BUY"         // 初次购买 - SUBSCRIBED
	SubTypeV2Resubscribe       = "RESUBSCRIBE"         // 重新订阅 - SUBSCRIBED
	SubTypeV2Downgrade         = "DOWNGRADE"           // 降级订阅(降级下个周期生效) - DID_CHANGE_RENEWAL_PREF
	SubTypeV2Upgrade           = "UPGRADE"             // 升级订阅(立即生效) - DID_CHANGE_RENEWAL_PREF
	SubTypeV2AutoRenewEnabled  = "AUTO_RENEW_ENABLED"  // 自动续订已启用 - DID_CHANGE_RENEWAL_STATUS
	SubTypeV2AutoRenewDisabled = "AUTO_RENEW_DISABLED" // 自动续订已禁用 - DID_CHANGE_RENEWAL_STATUS
	SubTypeV2Voluntary         = "VOLUNTARY"           // 因为用户关闭自动续期而过期 - EXPIRED
	SubTypeV2BillingRetry      = "BILLING_RETRY"       // 尝试扣费失败而过期 - EXPIRED
	SubTypeV2PriceIncrease     = "PRICE_INCREASE"      // 用户不同意涨价而过期 - EXPIRED
	SubTypeV2GracePeriod       = "GRACE_PERIOD"        // 宽限期 - DID_FAIL_TO_RENEW
	SubTypeV2BillingRecovery   = "BILLING_RECOVERY"    // 账单恢复 - DID_RENEW
	SubTypeV2Pending           = "PENDING"             // 等待用户同意价格上涨 - PRICE_INCREASE
	SubTypeV2Accepted          = "ACCEPTED"            // 接受 CONSUMPTION_REQUEST
)

// AppStoreTransaction 处理App Store支付事务
func (e *Entry) AppStoreTransaction(ctx *gin.Context, UID uint64, transId, env string) (*userDto.AdminUserListItem, error) {
	if err := e.RedisCli.Lock(ctx, redis.GetUserAppStoreTrance(UID, transId), redis.LockTimeHalfMinute); err != nil {
		return nil, ecode.UserAppStoreTraceIDUsedErr
	}
	defer e.RedisCli.Unlock(ctx, redis.GetUserAppStoreTrance(UID, transId))
	log.Ctx(ctx).WithField("uid", UID).WithField("transId", transId).Info("AppStoreTransactionStart")

	uInfo, err := e.UserSrv.GetUserInfo(ctx, UID)
	if err != nil {
		return nil, err
	}
	logTransInfo, err := e.PayLogRepo.FeatchByTransId(ctx, transId, payLog.StatusPaid)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("e.PayLogRepo.FeatchByTransId error")
		return nil, err
	}
	if logTransInfo.ID != dbs.False {
		return uInfo.ToUserInfoResp(ctx), nil
	}

	client, err := e.AppStoreExec.GetAppleClient(ctx, env)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("GetAppleClient error")
		return nil, err
	}

	transResp, err := client.GetTransactionInfo(ctx.Request.Context(), transId)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("GetTransactionInfo error")
		return nil, err
	}

	transInfo, err := transResp.DecodeSignedTransaction()
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("DecodeSignedTransaction error")
		return nil, err
	}
	log.Ctx(ctx).WithField("transResp", transResp).WithField("transInfo", transInfo).Info("GetTransactionInfo result")

	logOglTransInfo, err := e.PayLogRepo.FeatchByOriginalTransId(ctx, transInfo.OriginalTransactionId, payLog.StatusPaid)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("e.PayLogRepo.FeatchByOriginalTransId error")
		return nil, err
	}
	if logOglTransInfo.ID > 0 && logOglTransInfo.ExpiresTime == transInfo.ExpiresDate/1000 {
		return uInfo.ToUserInfoResp(ctx), nil
	}

	// 业务逻辑处理
	var (
		channelID, _    = helper.GetCtxChannelID(ctx)
		headers, _      = json.Marshal(ctx.Request.Header)
		reqData, _      = json.Marshal(map[string]string{"transId": transId})
		respData, _     = json.Marshal(transInfo)
		date            = timeUtil.NowToDateStringByZone(ctx)
		commonProductId = transInfo.ProductId
	)
	if channelID == uint64(common.ChannelViBoxIos) {
		commonProductId = productDao.ToCommonProductMap[productDao.MapProduct(transInfo.ProductId)]
	}

	expireTime, gracePeriod, err := e.getProductExpireTime(ctx, uInfo.VipTime, commonProductId)
	if err != nil {
		return nil, err
	}

	payLog := &payLog.Model{
		UserID:                uInfo.ID,
		Platform:              uint32(common.VipPlatApple),
		LogType:               uint32(payLog.LogRequest),
		Status:                uint32(payLog.StatusPaid),
		ProductID:             commonProductId,
		TransactionID:         transInfo.TransactionId,
		OriginalTransactionID: transInfo.OriginalTransactionId,
		PurchaseToken:         "",
		Amount:                transInfo.Price,
		Currency:              transInfo.Currency,
		NotifyID:              "",
		NotifyType:            "client",
		SubType:               "client",
		ExpiresTime:           transInfo.ExpiresDate / 1000,
		Env:                   transInfo.Environment,
		ChannelID:             channelID,
		TraceID:               helper.GetGinRequestID(ctx),
		Func:                  "GetTransactionInfo",
		Date:                  date,
		Header:                string(headers),
		ReqData:               string(reqData),
		RespData:              string(respData),
	}
	vipBill := &vipBill.Model{
		UserID:     uInfo.ID,
		Platform:   uint32(common.VipPlatApple),
		Amount:     transInfo.Price,
		Currency:   transInfo.Currency,
		Product:    commonProductId,
		Env:        transInfo.Environment,
		BillType:   vipBill.BtAppStorePay,
		Date:       date,
		BeforeTime: uInfo.VipTime,
		AfterTime:  expireTime,
		ChannelID:  channelID,
	}

	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	if err := func() (err error) {
		if err = e.PayLogRepo.CreateWithTx(ctx, tx, payLog); err != nil {
			return
		}

		vipBill.LogID = payLog.ID
		if err = e.VipBillRepo.CreateWithTx(ctx, tx, vipBill); err != nil {
			return
		}

		if err = e.UserRepo.UpdateMapByIDWithTx(ctx, tx, uInfo.ID, map[string]interface{}{
			"vip_time":     expireTime,
			"grace_period": gracePeriod,
		}); err != nil {
			return
		}

		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("AppStoreTransaction err")
		return nil, err
	}

	uInfo.VipTime = expireTime
	return uInfo.ToUserInfoResp(ctx), nil
}

// AppStoreNotify .
func (e *Entry) AppStoreNotify(ctx *gin.Context, signedPayload string) error {
	payload, err := apple.DecodeSignedPayload(signedPayload)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("apple.DecodeSignedPayload error")
		return err
	}
	renewalInfo, _ := payload.DecodeRenewalInfo()
	transInfo, err := payload.DecodeTransactionInfo()
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("payload.DecodeTransactionInfo error")
		return err
	}

	log.Ctx(ctx).WithFields(logrus.Fields{
		"renewalInfo":     renewalInfo,
		"transactionInfo": transInfo,
		"ntfType":         payload.NotificationType,
		"ntfSubType":      payload.Subtype,
		"ntfUUID":         payload.NotificationUUID,
	}).Info("apple.DecodeSignedPayload")

	var (
		decodeResp = &AppleNotifyDecodeRest{
			RenewalInfo: renewalInfo,
			TransInfo:   transInfo,
		}
		headers, _  = json.Marshal(ctx.Request.Header)
		respData, _ = json.Marshal(decodeResp)
		date        = timeUtil.NowToDateStringByZone(ctx)

		uInfo                = &user.Model{}
		logTransInfo         = &payLog.Model{}
		updateUser      bool = true
		logStatus            = uint32(payLog.StatusPaid)
		channelID, _         = helper.GetCtxChannelID(ctx)
		commonProductId      = transInfo.ProductId
	)

	if uInfo, err = e.UserRepo.FetchByUUID(ctx, transInfo.AppAccountToken); err != nil {
		log.Ctx(ctx).WithError(err).Error("e.UserRepo.FetchByUUID error")
		return err
	}
	if uInfo.ID == 0 {
		log.Ctx(ctx).WithField("transInfo.AppAccountToken", transInfo.AppAccountToken).Error("UUIDNotFound")
		return ecode.ParamInvalidErr
	}

	if channelID == uint64(common.ChannelViBoxIos) {
		commonProductId = productDao.ToCommonProductMap[productDao.MapProduct(transInfo.ProductId)]
	}

	expireTime, gracePeriod, err := e.getProductExpireTime(ctx, uInfo.VipTime, commonProductId)
	if err != nil {
		return err
	}
	if logTransInfo, err = e.PayLogRepo.FeatchByTransId(ctx, transInfo.TransactionId, payLog.StatusPaid); err != nil {
		log.Ctx(ctx).WithError(err).Error("e.PayLogRepo.FeatchByTransId error")
		return err
	}
	/*
		初次购买时，携带的购买成功的交易信息
		升级时，携带的升级之后，购买的高级商品的交易信息。原低级产品需要自己通过交易历史查询得到
		降级时，不会马上生效，携带的是上次购买成功的交易信息
		关闭自动续费时，携带的是上次购买成功的交易信息
		退款时，携带的是上次购买成功的交易信息（这是我猜的，没有实测过，因为无法触发退款通知）
		续订时，携带的是本次购买的交易信息
	*/
	switch payload.NotificationType {
	// 一次性购买
	case NotificationTypeOneTimeCharge:
		if logTransInfo.TransactionID == transInfo.TransactionId {
			updateUser, logStatus = false, uint32(payLog.StatusRepeat)
		}
	// 订阅升级(立即生效) or  降价(下个周期生效) or 不变
	case apple.NotificationTypeV2DidChangeRenewalPref:
		// 升级订阅，立即生效
		if payload.Subtype == apple.SubTypeV2Upgrade {
			if logTransInfo.TransactionID == transInfo.TransactionId {
				updateUser, logStatus = false, uint32(payLog.StatusRepeat)
			}
		} else if payload.Subtype == apple.SubTypeV2Downgrade {
			// 降级订阅，下个周期生效
		}
	// 用户已订阅, 用户续订
	case apple.NotificationTypeV2Subscribed, apple.NotificationTypeV2DidRenew:
		if logTransInfo.TransactionID == transInfo.TransactionId {
			updateUser, logStatus = false, uint32(payLog.StatusRepeat)
		}
	// 成功退款
	case apple.NotificationTypeV2Refund:
		updateUser, logStatus = false, uint32(payLog.StatusIgnore)
	// 用户发起了 购买或自动续订 的退款请求, 修改自动订阅状态
	case apple.NotificationTypeV2ConsumptionRequest, apple.NotificationTypeV2DidChangeRenewalStatus:
		updateUser, logStatus = false, uint32(payLog.StatusIgnore)
	// 自动续期失败, 用户的订阅过期,用户在宽限期内未能恢复订阅
	case apple.NotificationTypeV2DidFailToRenew, apple.NotificationTypeV2Expired, apple.NotificationTypeV2GracePeriodExpired:
		updateUser, logStatus = false, uint32(payLog.StatusIgnore)
	// 客户兑换了订阅优惠, 系统已通知客户自动续订订阅价格上涨, 拒绝退款
	case apple.NotificationTypeV2OfferRedeemed, apple.NotificationTypeV2PriceIncrease, apple.NotificationTypeV2RefundDeclined:
		updateUser, logStatus = false, uint32(payLog.StatusIgnore)
	// 续订期延长, 订阅购买者撤销了家庭共享
	case apple.NotificationTypeV2RenewalExtended, apple.NotificationTypeV2Revoke:
		updateUser, logStatus = false, uint32(payLog.StatusIgnore)
	default:
		updateUser, logStatus = false, uint32(payLog.StatusIgnore)
	}

	// 业务逻辑处理
	payLog := &payLog.Model{
		UserID:                uInfo.ID,
		Platform:              uint32(common.VipPlatApple),
		LogType:               uint32(payLog.LogNotify),
		Status:                logStatus,
		ProductID:             commonProductId,
		TransactionID:         transInfo.TransactionId,
		OriginalTransactionID: transInfo.OriginalTransactionId,
		PurchaseToken:         "",
		Amount:                transInfo.Price,
		Currency:              transInfo.Currency,
		NotifyID:              payload.NotificationUUID,
		NotifyType:            payload.NotificationType,
		SubType:               payload.Subtype,
		ExpiresTime:           transInfo.ExpiresDate / 1000,
		Env:                   transInfo.Environment,
		ChannelID:             channelID,
		TraceID:               helper.GetGinRequestID(ctx),
		Func:                  "DecodeSignedPayload",
		Date:                  date,
		Header:                string(headers),
		ReqData:               signedPayload,
		RespData:              string(respData),
	}
	vipBill := &vipBill.Model{
		UserID:     uInfo.ID,
		Platform:   uint32(common.VipPlatApple),
		Amount:     transInfo.Price,
		Currency:   transInfo.Currency,
		Product:    commonProductId,
		Env:        transInfo.Environment,
		BillType:   vipBill.BtAppStorePay,
		Date:       date,
		BeforeTime: uInfo.VipTime,
		AfterTime:  expireTime,
		ChannelID:  channelID,
	}

	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	if err := func() (err error) {
		if err = e.PayLogRepo.CreateWithTx(ctx, tx, payLog); err != nil {
			return
		}
		vipBill.LogID = payLog.ID

		if updateUser {
			if err = e.VipBillRepo.CreateWithTx(ctx, tx, vipBill); err != nil {
				return
			}

			if err = e.UserRepo.UpdateMapByIDWithTx(ctx, tx, uInfo.ID, map[string]interface{}{
				"vip_time":     expireTime,
				"grace_period": gracePeriod,
			}); err != nil {
				return
			}
		}

		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("AppStoreNotify err")
		return err
	}

	return nil
}
