package user

type AdminUserFilterReq struct {
	UserID     uint64 `form:"user_id" json:"user_id"`
	UserName   string `form:"user_name" json:"user_name"`
	Account    string `form:"account" json:"account"`
	Mobile     string `form:"mobile" json:"mobile"`
	UserTypeID uint64 `form:"user_type_id" json:"user_type_id"`
	ManagerID  uint64 `form:"manager_id" json:"manager_id"`
}

type AdminUserListReq struct {
	ID       uint64 `form:"id" json:"id"`
	Nickname string `form:"nickname" json:"nickname"`
	Mobile   string `form:"mobile" json:"mobile"`
	Email    string `form:"email" json:"email"`
	Status   uint32 `form:"status" json:"status"`
	Page     int    `form:"page" json:"page"`
	Limit    int    `form:"limit" json:"limit"`
}

type AdminUserListItem struct {
	ID            uint64 `json:"id"`
	UUID          string `json:"uuid"`
	Nickname      string `json:"nickname"`
	Avatar        string `json:"avatar"`
	Mobile        string `json:"mobile"`
	Email         string `json:"email"`
	Status        uint32 `json:"status"`
	GracePeriod   int64  `json:"grace_period"`
	VipTime       int64  `json:"vip_time"`
	WatchAdTime   int64  `json:"watch_ad_time"`
	LastLoginTime string `json:"last_login_time"`
	// CreatedAt     string `json:"created_at"`
}

type AdminUserListResp struct {
	List  []*AdminUserListItem `json:"list"`
	Total int64                `json:"total"`
}

type AdminUserInfoReq struct {
	ID uint64 `form:"id" json:"id" binding:"required,omitempty"`
}

type AdminUserInfoResp struct {
	*AdminUserListItem
	Auths []*AdminUserAuthItem `json:"auths"`
}

type AdminUserAuthItem struct {
	channelID  uint32 `json:"-"`
	platformID uint32 `json:"-"`
	AuthType   uint32 `json:"auth_type,omitempty"`
	AuthUid    string `json:"auth_uid,omitempty"`
	AuthToken  string `json:"auth_token,omitempty"`
	AuthEmail  string `json:"auth_email,omitempty"`
}

type AdminOperateUserReq struct {
	ID     uint64 `form:"id" json:"id" binding:"required,omitempty"`
	Action string `form:"action" json:"action" binding:"oneof=open close"`
	Value  uint32 `form:"value" json:"value" binding:"max=********"`
}

type DeviceListItem struct {
	ID          uint64 `json:"id"`
	DeviceID    string `json:"device_id"`
	WatchAdTime int64  `json:"watch_ad_time"`
}

// TODO 以下可移除

type AdminSetUserReq struct {
	ID          uint64 `form:"id" json:"id" binding:"omitempty"`
	Account     string `form:"account" json:"account" binding:"required,omitempty,max=32"`
	Pwd         string `form:"pwd" json:"pwd" binding:"omitempty,min=6,max=32"`
	Name        string `form:"name" json:"name" binding:"required,omitempty,max=32"`
	Mobile      string `form:"mobile" json:"mobile" binding:"required,omitempty"`
	CompanyID   uint64 `form:"company_id" json:"company_id" binding:"omitempty"`
	UserTypeID  uint64 `form:"user_type_id" json:"user_type_id" binding:"required,omitempty"`
	ManagerID   uint64 `form:"manager_id" json:"manager_id" binding:"required,omitempty"`
	Status      uint32 `form:"status" json:"status" binding:"oneof=1 2"`
	IdType      uint32 `form:"id_type" json:"id_type" binding:"oneof=1 9"`
	UserType    uint32 `form:"user_type" json:"user_type" binding:"oneof=1 2"`
	Deposit     uint64 `form:"deposit" json:"deposit"  binding:"max=***************"`
	License     string `form:"license" json:"license"`             // 营业执照
	IdCardFront string `form:"id_card_front" json:"id_card_front"` // 身份证正面照
	IdCardBack  string `form:"id_card_back" json:"id_card_back"`   // 身份证背面照
}

type AdminSetUserPwdReq struct {
	ID  uint64 `form:"id" json:"id" binding:"required,omitempty"`
	Pwd string `form:"pwd" json:"pwd" binding:"required,omitempty,min=6,max=32"`
}
