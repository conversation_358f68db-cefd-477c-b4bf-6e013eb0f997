package watchVideoPv

import (
	"time"
	"vlab/app/common/dbs"
)

// TODO 分表 or kafka异步写入es
type Model struct {
	ID         uint64    `gorm:"primarykey" json:"id"`
	EntityID   uint64    `json:"entity_id,omitempty"`
	EntityType uint32    `json:"entity_type,omitempty"`
	ShowID     uint64    `json:"show_id,omitempty"`
	EpisodeID  uint64    `json:"episode_id,omitempty"`
	Num        uint32    `json:"num,omitempty"`
	Date       string    `json:"date,omitempty"`
	Lang       string    `json:"lang,omitempty"`
	ClientType uint32    `json:"client_type,omitempty"`
	VersionID  uint64    `json:"version_id,omitempty"`
	ChannelID  uint64    `json:"channel_id,omitempty"`
	TraceID    string    `json:"trace_id,omitempty"`
	Header     string    `json:"header,omitempty"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "user_watch_video_pv"
}

type Filter struct {
	ID   uint64
	Sort dbs.CommonSort
}

type ModelList []*Model
