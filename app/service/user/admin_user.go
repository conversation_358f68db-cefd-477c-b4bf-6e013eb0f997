package user

import (
	"vlab/app/common/dbs"
	userDao "vlab/app/dao/user"
	userDto "vlab/app/dto/user"
	"vlab/pkg/ecode"

	"github.com/gin-gonic/gin"
)

// GetUserInfo .
func (e *Entry) GetUserInfo(ctx *gin.Context, UID uint64) (*userDao.Model, error) {
	uInfo, err := e.UserRepo.RedisUserInfo(ctx, UID)
	if err != nil {
		return nil, err
	}
	if uInfo.ID == 0 {
		return nil, ecode.ParamErr
	}

	return uInfo, nil
}

// AdminUserList .
func (e *Entry) AdminUserList(ctx *gin.Context, req userDto.AdminUserListReq) (*userDto.AdminUserListResp, error) {
	total, list, err := e.UserRepo.DataPageList(ctx, &userDao.Filter{
		ID:        req.ID,
		Nickname:  req.Nickname,
		Mobile:    req.Mobile,
		EmailLike: req.Email,
		Status:    req.Status,
	}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	retList := make([]*userDto.AdminUserListItem, 0, len(list))
	for _, val := range list {
		item := val.ToUserInfoResp(ctx)
		retList = append(retList, item)
	}
	return &userDto.AdminUserListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminUserInfo .
func (e *Entry) AdminUserInfo(ctx *gin.Context, UID uint64) (*userDto.AdminUserInfoResp, error) {
	var (
		res = &userDto.AdminUserInfoResp{
			AdminUserListItem: &userDto.AdminUserListItem{},
			Auths:             make([]*userDto.AdminUserAuthItem, 0),
		}
		uInfo = &userDao.Model{}
		err   error
	)

	if uInfo, err = e.GetUserInfo(ctx, UID); err != nil {
		return nil, err
	}

	res.AdminUserListItem = uInfo.ToUserInfoResp(ctx)

	res.Auths = e.GetUserAuthMethods(ctx, UID)

	return res, nil
}

// AdminOperateUser .
func (e *Entry) AdminOperateUser(ctx *gin.Context, id uint64, action dbs.OperateAction, val uint32) error {
	switch action {
	case dbs.ActionOpen, dbs.ActionClose:
		if err := e.UserRepo.UpdateMapByID(ctx, id, map[string]interface{}{"status": dbs.OperateActionMap[action]}); err != nil {
			return err
		}
		if action == dbs.ActionClose {
			return e.UserRepo.RedisClearUserAllToken(ctx, id)
		}
		// case dbs.ActionSort:
		// 	return e.UserRepo.UpdateMapByID(ctx, id, map[string]interface{}{"sort": val})
	}
	return nil
}

// AdminSetUser .
// func (e *Entry) AdminSetUser(ctx *gin.Context, req userDto.AdminSetUserReq) error {
// 	var (
// 		eg         errgroup.Group
// 		num        int64
// 		err        error
// 		m          = userDao.SetUserReqToModel(req)
// 		clearToken bool
// 	)
// 	if req.ID > 0 {
// 		eg.Go(func() (err error) {
// 			if num, err = e.UserRepo.CountByFilter(ctx, &userDao.Filter{Email: req.Account, NotID: req.ID}); err != nil {
// 				return
// 			}
// 			if num > 0 {
// 				return ecode.UserExistErr
// 			}
// 			return
// 		})
// 	} else {
// 		eg.Go(func() (err error) {
// 			if num, err = e.UserRepo.CountByFilter(ctx, &userDao.Filter{Email: req.Account}); err != nil {
// 				return
// 			}
// 			if num > 0 {
// 				return ecode.UserExistErr
// 			}
// 			return
// 		})
// 	}

// 	if err := eg.Wait(); err != nil {
// 		return err
// 	}

// 	if err = e.UserRepo.CreateOrUpdate(ctx, m, clearToken); err != nil {
// 		return err
// 	}

// 	return nil
// }

// // AdminSetUserPwd .
// func (e *Entry) AdminSetUserPwd(ctx *gin.Context, UID uint64, pwd string) error {
// 	enPwd, _ := helper.AesCbcEncrypt(pwd, helper.AesKey, helper.AesIV)
// 	return e.UserRepo.UpdateMapByID(ctx, UID, map[string]interface{}{"password": enPwd})
// }

// AdminUserSearch .
// func (e *Entry) AdminUserSearch(ctx *gin.Context, req *userDto.AdminUserSearchReq) ([]*userDto.AdminUserSearchItem, error) {
// 	retList := []*userDto.AdminUserSearchItem{}
// 	filter := &userDao.Filter{
// 		Nickname: req.Name,
// 		Status:   uint32(dbs.StatusEnable),
// 	}

// 	list, err := e.UserRepo.FindByFilter(ctx, filter)
// 	if err != nil {
// 		return nil, err
// 	}

// 	for _, val := range list {
// 		retList = append(retList, &userDto.AdminUserSearchItem{
// 			ID:   val.ID,
// 			Name: val.Nickname,
// 		})
// 	}

// 	return retList, nil
// }

func (e *Entry) GetDeviceInfo(ctx *gin.Context, deviceID string) (*userDto.DeviceListItem, error) {
	dInfo, err := e.DeviceRepo.RedisDeviceInfo(ctx, deviceID)
	if err != nil {
		return nil, err
	}

	return &userDto.DeviceListItem{
		ID:          dInfo.ID,
		DeviceID:    dInfo.DeviceID,
		WatchAdTime: dInfo.WatchAdTime,
	}, nil
}
