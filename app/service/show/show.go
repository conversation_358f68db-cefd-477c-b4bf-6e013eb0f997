package show

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"sort"
	"strings"

	"github.com/sirupsen/logrus"
	"vlab/app/common/config"
	"vlab/app/common/dbs"
	adminConfigDao "vlab/app/dao/admin_config"
	assignDao "vlab/app/dao/content_assign"
	classDao "vlab/app/dao/content_class"
	classFieldDao "vlab/app/dao/content_class_field"
	creditDao "vlab/app/dao/content_credit"
	episodeDao "vlab/app/dao/content_episode"
	franchiseDao "vlab/app/dao/content_franchise"
	i18nDao "vlab/app/dao/content_i18n"
	imageDao "vlab/app/dao/content_image"
	personDao "vlab/app/dao/content_person"
	popularDao "vlab/app/dao/content_popular"
	posterDao "vlab/app/dao/content_poster"
	recommendDao "vlab/app/dao/content_recommend"
	showDao "vlab/app/dao/content_show"
	channelkey "vlab/app/dao/resource_channel_key"
	versionDao "vlab/app/dao/resource_version"
	"vlab/app/dto"
	"vlab/app/service/resource"
	"vlab/app/service/search"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"
	strUtil "vlab/pkg/util/str_util"

	"github.com/samber/lo"
	"gorm.io/gorm/clause"

	showDto "vlab/app/dto/show"

	"github.com/gin-gonic/gin"
)

// ShowListCommonData 包含剧集列表查询的公共数据
type ShowListCommonData struct {
	I18nKeyList  []string
	I18nKeys     i18nDao.I18nList
	I18nKeyMap   map[string]string
	Posters      posterDao.PosterList
	ImageIDs     []uint64
	Images       imageDao.ImageList
	ShowIDs      []uint64
	FranchiseIDs []uint64
	Franchises   franchiseDao.FranchiseList
	WithGenres   showDao.ShowWithClassList
	GenreIDs     []uint64
	Genres       classFieldDao.ModelList
	WithFields   showDao.ShowWithClassList
	WithFieldMap map[uint64]showDao.ShowWithClassList
	FieldIDs     []uint64
	Fields       classFieldDao.ModelList
	FieldMap     map[uint64]classFieldDao.ModelList
}

// ShowListRequestContext 包含剧集列表请求的公共上下文信息
type ShowListRequestContext struct {
	ChannelKeyInfo *channelkey.Model
	ChannelID      uint64
	VersionID      uint64
	VersionModel   *versionDao.Model
	ISO639_1       string
}

// validateShowListRequest 验证剧集列表请求的公共参数
func (e Entry) validateShowListRequest(ctx *gin.Context) (*ShowListRequestContext, error) {
	channelKeyInfo, _ := helper.GetCtxChannelKeyInfo(ctx, false)
	if !channelKeyInfo.CheckOssImageHost() {
		return nil, ecode.ChannelConfigInvalidErr
	}

	chanID, _ := helper.GetCtxChannelID(ctx)
	versionID, _ := helper.GetCtxVersionID(ctx)

	verModels, err := resource.GetService().GetVersions(ctx, []uint64{versionID})
	if err != nil {
		return nil, err
	}
	verMap := verModels.GetIDMap()
	verModel, ok := verMap[versionID]
	if !ok {
		return nil, ecode.NotFoundErr
	}

	lang := config.GetLang(ctx)

	return &ShowListRequestContext{
		ChannelKeyInfo: channelKeyInfo,
		ChannelID:      chanID,
		VersionID:      versionID,
		VersionModel:   verModel,
		ISO639_1:       string(lang),
	}, nil
}

// applyVersionFilter 根据版本状态应用过滤逻辑
func (e Entry) applyVersionFilter(ctx *gin.Context, reqCtx *ShowListRequestContext, filter *showDao.Filter) ([]uint64, error) {
	var pluckShowIDs []uint64
	var err error

	if t := reqCtx.VersionModel; t != nil {
		switch t.Status {
		case uint32(dbs.StatusEnable): // 若该版本已上线, 则需要正常返回, 不需要过滤
			filter.ChannelID = 0 // 若该版本已上线, 则不需要过滤渠道
		case uint32(dbs.StatusDisable): // 若该版本已下线, 则需要返回空
			return nil, nil // 返回 nil 表示应该返回空结果
		case uint32(dbs.StatusAuditIng): // 若该版本在审核中, 则需要过滤渠道, 直接展示审核剧
			// 对于需要中间表查询的方法，需要获取 pluckShowIDs
			if filter.ChannelID > 0 {
				pluckShowIDs, err = e.ShowRepo.FindXidsByFilter(ctx, filter, "content_show.id")
				if err != nil {
					return nil, err
				}
			}
		}
	}

	return pluckShowIDs, nil
}

// buildShowListCommonData 构建剧集列表的公共关联数据
func (e Entry) buildShowListCommonData(ctx *gin.Context, showList showDao.ShowList, iso639_1 string, includeFields bool) (*ShowListCommonData, error) {
	data := &ShowListCommonData{
		I18nKeyList:  make([]string, 0),
		I18nKeys:     make(i18nDao.I18nList, 0),
		I18nKeyMap:   make(map[string]string),
		Posters:      make(posterDao.PosterList, 0),
		ImageIDs:     make([]uint64, 0),
		Images:       make(imageDao.ImageList, 0),
		ShowIDs:      showList.GetIDs(),
		FranchiseIDs: make([]uint64, 0),
		Franchises:   make(franchiseDao.FranchiseList, 0),
		WithGenres:   make(showDao.ShowWithClassList, 0),
		GenreIDs:     make([]uint64, 0),
		Genres:       make(classFieldDao.ModelList, 0),
		WithFields:   make(showDao.ShowWithClassList, 0),
		WithFieldMap: make(map[uint64]showDao.ShowWithClassList),
		FieldIDs:     make([]uint64, 0),
		Fields:       make(classFieldDao.ModelList, 0),
		FieldMap:     make(map[uint64]classFieldDao.ModelList),
	}

	var err error

	// 收集基础的 i18n keys 和 franchise IDs
	for _, showModel := range showList {
		data.I18nKeyList = append(data.I18nKeyList, showModel.NameKey, showModel.OverviewKey)
		data.FranchiseIDs = append(data.FranchiseIDs, showModel.FranchiseID)
	}

	// 查询 franchise 数据
	if t := lo.Uniq(data.FranchiseIDs); len(t) > 0 {
		data.Franchises, err = e.FranchiseRepo.FindByFilter(ctx, &franchiseDao.Filter{
			IDs: t,
		})
		if err != nil {
			return nil, err
		}
		data.I18nKeyList = append(data.I18nKeyList, data.Franchises.GetNameKeys()...)
	}

	// 查询 genres 数据
	if t := lo.Uniq(data.ShowIDs); len(t) > 0 {
		data.WithGenres, err = e.ShowRepo.FindClassByFilter(ctx, &showDao.ClassFilter{
			ShowIDs: t,
			ClassID: 5, // 5是类型
		})
		if err != nil {
			return nil, err
		}
		data.GenreIDs = data.WithGenres.GetFieldIDs()

		if len(data.GenreIDs) > 0 {
			data.Genres, err = e.ClassFieldRepo.FindByFilter(ctx, &classFieldDao.Filter{
				IDs: data.GenreIDs,
			})
			if err != nil {
				return nil, err
			}
			data.I18nKeyList = append(data.I18nKeyList, data.Genres.GetNameKeys()...)
		}
	}

	// 查询 fields 数据（如果需要）
	if t := lo.Uniq(data.ShowIDs); includeFields && len(t) > 0 {
		data.WithFields, err = e.ShowRepo.FindClassByFilter(ctx, &showDao.ClassFilter{
			ShowIDs: t,
		})
		if err != nil {
			return nil, err
		}
		data.FieldIDs = data.WithFields.GetFieldIDs()
		data.WithFieldMap = data.WithFields.GetShowIDMap()

		if len(data.FieldIDs) > 0 {
			data.Fields, err = e.ClassFieldRepo.FindByFilter(ctx, &classFieldDao.Filter{
				IDs: data.FieldIDs,
			})
			if err != nil {
				return nil, err
			}
			data.I18nKeyList = append(data.I18nKeyList, data.Fields.GetNameKeys()...)
			data.FieldMap = data.Fields.GetIDMap()
		}
	}

	// 查询 posters 和 images
	if t := lo.Uniq(data.ShowIDs); len(t) > 0 {
		data.Posters, err = e.PosterRepo.FindByFilter(ctx, &posterDao.Filter{
			ShowIDs: t,
		})
		if err != nil {
			return nil, err
		}
		data.ImageIDs = append(data.ImageIDs, data.Posters.GetImageIDs()...)
	}

	if t := lo.Uniq(data.ImageIDs); len(t) > 0 {
		data.Images, err = e.ImageRepo.FindByFilter(ctx, &imageDao.Filter{
			IDs: t,
		})
		if err != nil {
			return nil, err
		}
	}

	// 查询 i18n 数据
	if t := lo.Uniq(data.I18nKeyList); len(t) > 0 {
		data.I18nKeys, err = e.I18nRepo.FindByFilterWithFallback(ctx, &i18nDao.Filter{
			Keys:      t,
			ISO_639_1: iso639_1,
		}, "en_US")
		if err != nil {
			return nil, err
		}
	}

	data.I18nKeyMap = data.I18nKeys.GetI18nKeyMap()

	return data, nil
}

func (e Entry) buildFilterList(ctx *gin.Context, req *showDto.ShowListReq, filter *showDao.Filter) {
	if filter.Sort == nil || len(filter.Sort) <= 0 {
		filter.Sort = make([]clause.OrderByColumn, 0)
	}

	model := &showDao.Show{}

	if t := req.SortReq; t.Type > 0 {
		sortType := t.Type

		switch sortType {
		case 1: // 最新
			filter.Sort = append(filter.Sort, clause.OrderByColumn{
				Column: clause.Column{
					Table: model.TableName(),
					Name:  "air_date_ts",
				},
				Desc: req.CheckOrder(),
			})

		case 2: // 最热
			filter.Sort = append(filter.Sort, clause.OrderByColumn{
				Column: clause.Column{
					Table: model.TableName(),
					Name:  "created_at",
				},
				Desc: req.CheckOrder(),
			})
		case 3: // 评分
			filter.Sort = append(filter.Sort, clause.OrderByColumn{
				Column: clause.Column{
					Table: model.TableName(),
					Name:  "score",
				},
				Desc: req.CheckOrder(),
			})

		default:

		}
		filter.Sort = append(filter.Sort, clause.OrderByColumn{
			Column: clause.Column{
				Table: model.TableName(),
				Name:  "id",
			},
			Desc: true,
		})
	}

	return
}

// shouldUseVectorSearch 判断是否应该使用向量搜索
func (e Entry) shouldUseVectorSearch(ctx *gin.Context, req *showDto.ShowSearchReq) bool {
	// 首先检查配置开关
	configValue := adminConfigDao.RedisGetConfig[string](ctx, adminConfigDao.KeyEnableShowSearchVector)
	if configValue != "1" && configValue != "true" {
		return false
	}

	// 如果没有关键词，不使用向量搜索
	if req.Keyword == "" {
		return false
	}

	// 如果明确设置了UseVector参数，以该参数为准
	if req.UseVector {
		return true
	}

	// 默认策略：当关键词长度大于2个字符时启用向量搜索
	// 这可以避免对单个字符或很短查询进行向量搜索
	trimmedKeyword := strings.TrimSpace(req.Keyword)
	if len(trimmedKeyword) <= 2 {
		return false
	}

	// 检查关键词是否包含中文字符，中文查询更适合向量搜索
	hasChineseChar := false
	for _, r := range trimmedKeyword {
		if r >= 0x4e00 && r <= 0x9fff {
			hasChineseChar = true
			break
		}
	}

	// 中文查询或较长的英文查询适合使用向量搜索
	return hasChineseChar || len(trimmedKeyword) > 5
}

func (e Entry) ClassList(ctx *gin.Context, req *showDto.ClassListReq) (resp *showDto.ClassListResp, err error) {
	resp = &showDto.ClassListResp{}
	resp.List = make([]*showDto.ClassListItem, 0)

	cnt, list, err := e.ClassFieldRepo.DataPageList(ctx, &classFieldDao.Filter{}, 1, 999)
	if err != nil {
		return nil, err
	}
	resp.Total = uint64(cnt)

	if cnt == 0 || len(list) <= 0 {
		return
	}

	var (
		classIDs      = list.ClassIDs()
		classFieldMap = list.GetClassIDMap()

		i18nKeyList = make([]string, 0)
		i18nKeyMap  = make(map[string]string)
	)

	// 获取class的内容
	classList, err := e.ClassRepo.FindByFilter(ctx, &classDao.Filter{
		IDs: classIDs,
	})
	if err != nil {
		return nil, err
	}

	i18nKeyList = classList.GetNameKeys()
	i18nKeyList = append(i18nKeyList, list.GetNameKeys()...)

	i18ns, err := e.I18nRepo.FindByFilterWithFallback(ctx, &i18nDao.Filter{
		Keys:      i18nKeyList,
		ISO_639_1: string(config.GetLang(ctx)),
	}, "en_US")
	if err != nil {
		return nil, err
	}
	i18nKeyMap = i18ns.GetI18nKeyMap()

	for _, class := range classList {
		if fields, ok := classFieldMap[class.ID]; ok {
			classBase := &showDto.ClassListItem{
				ClassID: class.ID,
				Name:    class.Name,
				Total:   0,
				List:    make([]*showDto.ClassFieldItem, 0),
			}
			if name, ok := i18nKeyMap[class.NameKey]; ok {
				classBase.Name = name
			}
			for _, field := range fields {
				classField := &showDto.ClassFieldItem{
					ID:      field.ID,
					ClassId: field.ClassID,
					Name:    field.Name,
					Cover:   "",
					Desc:    "",
				}
				if name, ok := i18nKeyMap[field.NameKey]; ok {
					classField.Name = name
				} else {
					continue
				}
				classBase.Total++
				classBase.List = append(classBase.List, classField)
			}
			resp.List = append(resp.List, classBase)
		}
	}

	sort.Slice(resp.List, func(i, j int) bool {
		return resp.List[i].ClassID < resp.List[j].ClassID
	})

	resp.Sorts = resp.List[0]
	for _, item := range resp.Sorts.List {
		switch item.ID {
		// 搜索类型 1-最新 2-最热 3-评分
		case 1:
			item.SortReq.Type = 1
			item.SortReq.SortMethod = "desc"
		case 2:
			item.SortReq.Type = 2
			item.SortReq.SortMethod = "desc"
		case 3:
			item.SortReq.Type = 3
			item.SortReq.SortMethod = "desc"
		}
	}
	resp.List = resp.List[1:]

	return
}

func (e Entry) ShowList(ctx *gin.Context, req *showDto.ShowListReq) (resp *showDto.ShowListResp, err error) {
	resp = &showDto.ShowListResp{}
	resp.List = make([]*showDto.ShowBase, 0)

	// 验证请求参数
	reqCtx, err := e.validateShowListRequest(ctx)
	if err != nil {
		return nil, err
	}

	// 设置请求参数
	req.ChannelID = reqCtx.ChannelID
	req.VersionID = reqCtx.VersionID
	req.ISO_639_1 = reqCtx.ISO639_1

	// 设置过滤器
	filter := &showDao.Filter{
		Status:      uint32(dbs.StatusEnable),
		ChannelID:   req.ChannelID,
		FieldIDs:    req.FieldIDs,
		ContentType: req.ContentType,
	}

	// 应用版本过滤逻辑
	_, err = e.applyVersionFilter(ctx, reqCtx, filter)
	if err != nil {
		return nil, err
	}
	// 如果版本已下线，直接返回空结果
	if reqCtx.VersionModel.Status == uint32(dbs.StatusDisable) {
		return resp, nil
	}

	e.buildFilterList(ctx, req, filter)

	// 若有用户ID, 处理用户的限制

	cnt, list, err := e.ShowRepo.DataPageList(ctx, filter,
		req.Page, req.Limit)
	if err != nil {
		return nil, err
	}
	if cnt == 0 {
		return
	}
	resp.Count = cnt
	if len(list) <= 0 {
		return resp, nil
	}

	// 构建公共关联数据
	commonData, err := e.buildShowListCommonData(ctx, list, req.ISO_639_1, true)
	if err != nil {
		return nil, err
	}

	// 构建响应
	e.setShowListResp(
		resp,
		list,
		commonData.Posters,
		commonData.Images,
		commonData.Genres,
		commonData.WithGenres,
		commonData.Franchises,
		commonData.I18nKeyMap,
		reqCtx.ChannelKeyInfo,
	)

	// 获取剧集数量信息
	showEpisodeMap, err := e.getShowEpisodeInfo(ctx, commonData.ShowIDs)
	if err != nil {
		return resp, nil
	}
	if len(showEpisodeMap) > 0 {
		for _, show := range resp.List {
			if episodeCount, ok := showEpisodeMap[show.ID]; ok {
				show.NumberOfEpisodes = uint32(episodeCount)
			}
		}
	}

	// 处理分类信息
	if len(commonData.FieldMap) > 0 {
		for _, show := range resp.List {
			if show.Classes == nil || len(show.Classes) <= 0 {
				show.Classes = make([]*showDto.ClassBase, 0)
			}
			if withs, ok := commonData.WithFieldMap[show.ID]; ok {
				for _, with := range withs {
					if fields, ok := commonData.FieldMap[with.FieldID]; ok {
						for _, field := range fields {
							class := &showDto.ClassBase{
								Id:      field.ID,
								ClassID: field.ClassID,
								Name:    field.Name,
							}

							if name, ok := commonData.I18nKeyMap[field.NameKey]; ok {
								class.Name = name
							}

							show.Classes = append(show.Classes, class)
						}
					}
				}
			}
		}
	}

	return resp, nil
}

func (e Entry) ShowDetail(ctx *gin.Context, req *showDto.ShowDetailReq) (resp *showDto.ShowDetailResp, err error) {
	resp = &showDto.ShowDetailResp{}

	channelKeyInfo, _ := helper.GetCtxChannelKeyInfo(ctx, false)
	if !channelKeyInfo.CheckOssImageHost() {
		return nil, ecode.ChannelConfigInvalidErr
	}

	chanID, _ := helper.GetCtxChannelID(ctx)
	req.ChannelID = chanID

	versionID, _ := helper.GetCtxVersionID(ctx)
	req.VersionID = versionID

	verModels, err := resource.GetService().GetVersions(ctx, []uint64{
		req.VersionID,
	})
	if err != nil {
		return nil, err
	}
	verMap := verModels.GetIDMap()
	verModel, ok := verMap[req.VersionID]
	if !ok {
		return nil, ecode.NotFoundErr
	}

	lang := config.GetLang(ctx)
	req.ISO_639_1 = string(lang)

	var (
		limits    = make(showDao.ShowWithLimitList, 0)
		showModel = &showDao.Show{}
		credits   = make(creditDao.CreditList, 0)
		//seasons     = []*seasonDao.Season{}
		posters     = make(posterDao.PosterList, 0)
		i18nKeyList = make([]string, 0)
		i18nKeys    = make(i18nDao.I18nList, 0)
		i18nKeyMap  = make(map[string]string)
		personIDs   = make([]uint64, 0)
		persons     = make(personDao.PersonList, 0)
		episodes    = make([]*episodeDao.Episode, 0)

		imageIDs = make([]uint64, 0)
		images   = make([]*imageDao.Image, 0)

		franchises = make(franchiseDao.FranchiseList, 0)
		withGenres = make(showDao.ShowWithClassList, 0)
		genreIDs   = make([]uint64, 0)
		genres     = make(classFieldDao.ModelList, 0)
	)

	if t := verModel; t != nil {
		switch t.Status {
		case uint32(dbs.StatusEnable): // 若该版本已上线, 则需要正常返回, 不需要过滤
			//filter.AuditType = uint32(showDao.LimitTypeAuditStatusNone)
		case uint32(dbs.StatusDisable): // 若该版本已下线, 则需要返回空
			return resp, nil
		case uint32(dbs.StatusAuditIng): // 若该版本在审核中, 则需要过滤渠道, 直接展示审核剧
			//filter.AuditType = uint32(showDao.LimitTypeAuditStatusProcessing)
			// 若有用户ID, 处理用户的限制
			limits, err = e.ShowRepo.FindLimitByFilter(ctx, &showDao.LimitFilter{
				ShowID:  req.ShowID,
				LimitID: req.ChannelID,
				Type:    uint32(showDao.LimitTypeChannel),
			})
			if err != nil {
				return nil, err
			}
			if len(limits) <= 0 {
				return resp, nil
			}
		}
	}

	showModel, err = e.ShowRepo.FetchByID(ctx, req.ShowID)
	if err != nil {
		return nil, err
	}

	i18nKeyList = append(i18nKeyList,
		showModel.NameKey, showModel.OverviewKey, showModel.AirDateKey)
	{
		// franchise
		if showModel.FranchiseID > 0 {
			franchises, err = e.FranchiseRepo.FindByFilter(ctx, &franchiseDao.Filter{
				ID: showModel.FranchiseID,
			})
			i18nKeyList = append(i18nKeyList, franchises.GetNameKeys()...)
		}

		// genres
		withGenres, err = e.ShowRepo.FindClassByFilter(ctx, &showDao.ClassFilter{
			ShowID:  req.ShowID,
			ClassID: 5, // 5是类型
		})
		genreIDs = withGenres.GetFieldIDs()

		if len(genreIDs) > 0 {
			genres, err = e.ClassFieldRepo.FindByFilter(ctx, &classFieldDao.Filter{
				IDs: genreIDs,
			})
			i18nKeyList = append(i18nKeyList, genres.GetNameKeys()...)
		}
	}

	credits, err = e.CreditRepo.FindByFilter(ctx, &creditDao.Filter{
		ShowID: req.ShowID,
	})
	personIDs = credits.GetPersonIDs()

	if len(personIDs) > 0 {
		persons, err = e.PersonRepo.FindByFilter(ctx, &personDao.Filter{
			IDs: personIDs,
		})

		imageIDs = append(imageIDs, persons.GetImageIDs()...)
	}

	for _, person := range persons {
		i18nKeyList = append(i18nKeyList, person.NameKey)
	}

	posters, err = e.PosterRepo.FindByFilter(ctx, &posterDao.Filter{
		ShowID: req.ShowID,
	})

	imageIDs = append(imageIDs, posters.GetImageIDs()...)

	if len(imageIDs) > 0 {
		images, err = e.ImageRepo.FindByFilter(ctx, &imageDao.Filter{
			IDs: imageIDs,
		})
	}

	episodes, err = e.EpisodeRepo.FindByFilter(ctx, &episodeDao.Filter{
		ShowID: req.ShowID,
		Status: uint32(dbs.StatusEnable),
	})

	if len(i18nKeyList) > 0 {
		// 校验ISO_639_1
		i18nKeys, err = e.I18nRepo.FindByFilterWithFallback(ctx, &i18nDao.Filter{
			Keys:      i18nKeyList,
			ISO_639_1: req.ISO_639_1,
		}, "en_US")
	}

	i18nKeyMap = i18nKeys.GetI18nKeyMap()

	e.setShowDetailResp(
		resp,
		showModel,
		credits,
		episodes,
		posters,
		persons,
		images,
		genres,
		withGenres,
		franchises,
		i18nKeyMap,
		channelKeyInfo,
	)

	sort.Slice(resp.Episodes, func(i, j int) bool {
		return resp.Episodes[i].EpisodeNumber < resp.Episodes[j].EpisodeNumber
	})

	showEpisodeMap, err := e.getShowEpisodeInfo(ctx, []uint64{req.ShowID})
	if err != nil {
		return resp, nil
	}
	if len(showEpisodeMap) > 0 {
		if episodeCount, ok := showEpisodeMap[req.ShowID]; ok {
			resp.NumberOfEpisodes = uint32(episodeCount)
		}
	}

	return resp, nil
}

func (e Entry) buildFilter(ctx *gin.Context, req *showDto.ShowSearchReq, filter *showDao.Filter) {
	if filter.Sort == nil || len(filter.Sort) <= 0 {
		filter.Sort = make([]clause.OrderByColumn, 0)
	}

	model := &showDao.Show{}

	if t := req.SortReq; t.Type > 0 {
		sortType := t.Type

		switch sortType {
		case 1: // 最新
			filter.Sort = append(filter.Sort, clause.OrderByColumn{
				Column: clause.Column{
					Table: model.TableName(),
					Name:  "air_date_ts",
				},
				Desc: req.CheckOrder(),
			})

		case 2: // 最热
			filter.Sort = append(filter.Sort, clause.OrderByColumn{
				Column: clause.Column{
					Table: model.TableName(),
					Name:  "created_at",
				},
				Desc: req.CheckOrder(),
			})
		case 3: // 评分
			filter.Sort = append(filter.Sort, clause.OrderByColumn{
				Column: clause.Column{
					Table: model.TableName(),
					Name:  "score",
				},
				Desc: req.CheckOrder(),
			})

		default:

		}
		filter.Sort = append(filter.Sort, clause.OrderByColumn{
			Column: clause.Column{
				Table: model.TableName(),
				Name:  "id",
			},
			Desc: true,
		})
	}

	return
}

func (e Entry) toShowBase(ctx *gin.Context, showID uint64, iso_639_1 string) (resp *showDto.ShowBase, err error) {
	var (
		showModel   = &showDao.Show{}
		i18nKeyList = make([]string, 0)
		i18nKeys    = make(i18nDao.I18nList, 0)
		i18nKeyMap  = make(map[string]string)

		withFranchise = make(showDao.ShowWithFranchiseList, 0)
		franchiseIDs  = make([]uint64, 0)
		franchises    = make(franchiseDao.FranchiseList, 0)
		withGenres    = make(showDao.ShowWithClassList, 0)
		genreIDs      = make([]uint64, 0)
		genres        = make(classFieldDao.ModelList, 0)

		posters  = make(posterDao.PosterList, 0)
		imageIDs = make([]uint64, 0)
		images   = make([]*imageDao.Image, 0)
	)
	resp = &showDto.ShowBase{}

	channelKeyInfo, _ := helper.GetCtxChannelKeyInfo(ctx, true)
	if !channelKeyInfo.CheckOssImageHost() {
		return nil, ecode.ChannelConfigInvalidErr
	}

	showModel, err = e.ShowRepo.FetchByID(ctx, showID)
	if err != nil {
		return nil, err
	}

	i18nKeyList = append(i18nKeyList,
		showModel.NameKey, showModel.OverviewKey)
	{
		// franchise
		withFranchise, err = e.ShowRepo.FindFranchiseByFilter(ctx, &showDao.FranchiseFilter{
			ShowID: showID,
		})
		franchiseIDs = withFranchise.GetFranchiseIDs()

		if len(franchiseIDs) > 0 {
			franchises, err = e.FranchiseRepo.FindByFilter(ctx, &franchiseDao.Filter{
				IDs: franchiseIDs,
			})
			i18nKeyList = append(i18nKeyList, franchises.GetNameKeys()...)
		}

		// genres
		withGenres, err = e.ShowRepo.FindClassByFilter(ctx, &showDao.ClassFilter{
			ShowID:  showID,
			ClassID: 5,
		})
		genreIDs = withGenres.GetFieldIDs()

		if len(genreIDs) > 0 {
			genres, err = e.ClassFieldRepo.FindByFilter(ctx, &classFieldDao.Filter{
				IDs: genreIDs,
			})
			i18nKeyList = append(i18nKeyList, genres.GetNameKeys()...)
		}
	}

	posters, err = e.PosterRepo.FindByFilter(ctx, &posterDao.Filter{
		ShowID: showID,
	})

	imageIDs = append(imageIDs, posters.GetImageIDs()...)

	if len(images) > 0 {
		images, err = e.ImageRepo.FindByFilter(ctx, &imageDao.Filter{
			IDs: imageIDs,
		})
	}

	// 校验ISO_639_1
	i18nKeys, err = e.I18nRepo.FindByFilterWithFallback(ctx, &i18nDao.Filter{
		Keys:      i18nKeyList,
		ISO_639_1: iso_639_1,
	}, "en_US")

	i18nKeyMap = i18nKeys.GetI18nKeyMap()

	e.setShowBase(
		ctx,
		resp,
		showModel,
		posters,
		images,
		genres,
		withGenres,
		franchises,
		withFranchise,
		i18nKeyMap,
		channelKeyInfo,
	)

	return
}

func (e Entry) setShowBase(
	ctx *gin.Context,
	resp *showDto.ShowBase,
	showModel *showDao.Show,
	posters posterDao.PosterList,
	images imageDao.ImageList,
	genres classFieldDao.ModelList,
	withGenres showDao.ShowWithClassList,
	franchises franchiseDao.FranchiseList,
	withFranchise showDao.ShowWithFranchiseList,
	i18nKeyMap map[string]string,
	channelKeyInfo *channelkey.Model,
) {
	var (
		imageMap         = images.GetMap()
		showGenreMap     = withGenres.GetShowIDMap()
		genreMap         = genres.GetMap()
		showFranchiseMap = withFranchise.GetShowIDMap()
		franchiseMap     = franchises.GetMap()
	)

	resp.Franchise = &showDto.FranchiseBase{}
	resp.Genres = make([]*showDto.GenreBase, 0)
	resp.Posters = make([]*showDto.ImageBase, 0)

	resp.ID = showModel.ID

	// Name 随语言
	if name, ok := i18nKeyMap[showModel.NameKey]; ok {
		resp.Name = name
	} else {
		resp.Name = showModel.Name
	}
	resp.OriginalName = showModel.Name
	// Overview 随语言
	if overview, ok := i18nKeyMap[showModel.NameKey]; ok {
		resp.Overview = overview
	} else {
		resp.Overview = showModel.Overview
	}

	resp.Score = showModel.Score
	resp.AirDate = showModel.AirDate
	resp.ContentType = uint32(showModel.ContentType)
	resp.InProduction = showModel.InProduction.Uint32()

	// genres
	if genres, ok := showGenreMap[showModel.ID]; ok {
		for _, genre := range genres {
			genreBase := &showDto.GenreBase{
				Id: genre.ID,
			}
			if genre, ok := genreMap[genre.FieldID]; ok {
				if name, ok := i18nKeyMap[genre.NameKey]; ok {
					genreBase.Name = name
				} else {
					genreBase.Name = genre.Name
				}
			}

			resp.Genres = append(resp.Genres, genreBase)
		}
	}

	// franchise
	if franchises, ok := showFranchiseMap[showModel.ID]; ok {
		for _, franchise := range franchises {
			resp.Franchise = &showDto.FranchiseBase{
				Id: franchise.ID,
			}
			if franchise, ok := franchiseMap[franchise.FranchiseID]; ok {
				if name, ok := i18nKeyMap[franchise.NameKey]; ok {
					resp.Franchise.Name = name
				} else {
					resp.Franchise.Name = franchise.Name
				}
			}
		}
	}

	// posters
	for _, poster := range posters {
		if image, ok := imageMap[poster.ImageID]; ok {
			resp.Posters = append(resp.Posters, &showDto.ImageBase{
				AspectRatio: image.AspectRatio,
				Height:      image.Height,
				Iso6391:     image.ISO_639_1,
				FilePath:    image.GetFilePath(channelKeyInfo.OssImageHost),
				Width:       image.Width,
				AspectType:  image.SetAspectType(),
			})
		}
	}

	return
}

func (e Entry) setShowListResp(
	resp *showDto.ShowListResp,
	showList showDao.ShowList,
	posters posterDao.PosterList,
	images imageDao.ImageList,
	genres classFieldDao.ModelList,
	withGenres showDao.ShowWithClassList,
	franchises franchiseDao.FranchiseList,
	i18nKeyMap map[string]string,
	channelKeyInfo *channelkey.Model,
) {
	var (
		imageMap      = images.GetMap()
		posterShowMap = posters.GetShowMap()
		showGenreMap  = withGenres.GetShowIDMap()
		genreMap      = genres.GetMap()
		franchiseMap  = franchises.GetMap()
	)
	for _, showModel := range showList {
		showBase := &showDto.ShowBase{}
		showBase.ID = showModel.ID

		// Name 随语言
		if name, ok := i18nKeyMap[showModel.NameKey]; ok {
			showBase.Name = name
		} else {
			showBase.Name = showModel.Name
		}
		showBase.OriginalName = showModel.Name
		// Overview 随语言
		if overview, ok := i18nKeyMap[showModel.OverviewKey]; ok {
			showBase.Overview = overview
		} else {
			showBase.Overview = showModel.Overview
		}

		showBase.Score = showModel.Score
		showBase.AirDate = showModel.AirDate
		showBase.ContentType = uint32(showModel.ContentType)
		showBase.InProduction = showModel.InProduction.Uint32()

		// genres
		if genres, ok := showGenreMap[showModel.ID]; ok {
			for _, genre := range genres {
				genreBase := &showDto.GenreBase{
					Id: genre.ID,
				}
				if genre, ok := genreMap[genre.FieldID]; ok {
					if name, ok := i18nKeyMap[genre.NameKey]; ok {
						genreBase.Name = name
					} else {
						genreBase.Name = genre.Name
					}
				}

				showBase.Genres = append(showBase.Genres, genreBase)
			}
		}

		// franchise
		if franchise, ok := franchiseMap[showModel.FranchiseID]; ok {
			showBase.Franchise = &showDto.FranchiseBase{
				Id: franchise.ID,
			}
			if name, ok := i18nKeyMap[franchise.NameKey]; ok {
				showBase.Franchise.Name = name
			} else {
				showBase.Franchise.Name = franchise.Name
			}
		}

		if posters, ok := posterShowMap[showModel.ID]; ok {
			for _, poster := range posters {
				if image, ok := imageMap[poster.ImageID]; ok {
					showBase.Posters = append(showBase.Posters, &showDto.ImageBase{
						AspectRatio: image.AspectRatio,
						Height:      image.Height,
						Iso6391:     image.ISO_639_1,
						FilePath:    image.GetFilePath(channelKeyInfo.OssImageHost),
						Width:       image.Width,
						AspectType:  image.SetAspectType(),
					})
				}
			}
		}
		resp.List = append(resp.List, showBase)
	}
	return
}

func (e Entry) setShowDetailResp(
	resp *showDto.ShowDetailResp,
	showModel *showDao.Show,
	credits []*creditDao.Credit,
	episodes episodeDao.EpisodeList,
	posters []*posterDao.Poster,
	persons personDao.PersonList,
	images imageDao.ImageList,
	genres classFieldDao.ModelList,
	withGenres showDao.ShowWithClassList,
	franchises franchiseDao.FranchiseList,
	i18nKeyMap map[string]string,
	channelKeyInfo *channelkey.Model,
) {

	var (
		imageMap       = images.GetMap()
		personMap      = persons.GetMap()
		episodeShowMap = episodes.GetShowKeyMap()

		genreMap     = genres.GetMap()
		franchiseMap = franchises.GetMap()
		showGenreMap = withGenres.GetShowIDMap()
	)

	resp.ShowBase = &showDto.ShowBase{}
	resp.Franchise = &showDto.FranchiseBase{}
	resp.Credits = &showDto.Credits{}
	resp.Images = &showDto.Images{}
	resp.Images.Posters = make([]*showDto.ImageBase, 0)
	resp.Images.BackDrops = make([]*showDto.ImageBase, 0)
	resp.Credits.Casts = make([]*showDto.CreditBase, 0)
	resp.Credits.Crews = make([]*showDto.CreditBase, 0)
	resp.ShowBase.Genres = make([]*showDto.GenreBase, 0)
	resp.ShowBase.Posters = make([]*showDto.ImageBase, 0)
	resp.CreatedBy = make([]*showDto.CreditBase, 0)
	resp.Episodes = make([]*showDto.EpisodeBase, 0)
	//resp.Seasons = make([]*showDto.SeasonBase, 0)

	resp.ID = showModel.ID

	// Name 随语言
	if name, ok := i18nKeyMap[showModel.NameKey]; ok {
		resp.Name = name
	} else {
		resp.Name = showModel.Name
	}
	resp.OriginalName = showModel.Name
	// Overview 随语言
	if overview, ok := i18nKeyMap[showModel.OverviewKey]; ok {
		resp.Overview = overview
	} else {
		resp.Overview = showModel.Overview
	}

	resp.Score = showModel.Score
	resp.AirDate = showModel.AirDate
	resp.ContentType = uint32(showModel.ContentType)
	resp.InProduction = showModel.InProduction.Uint32()

	resp.Homepage = showModel.Homepage

	// genres
	if genres, ok := showGenreMap[showModel.ID]; ok {
		for _, withGenre := range genres {
			genreBase := &showDto.GenreBase{}
			if genre, ok := genreMap[withGenre.FieldID]; ok {
				genreBase.Id = withGenre.FieldID
				if name, ok := i18nKeyMap[genre.NameKey]; ok {
					genreBase.Name = name
				} else {
					genreBase.Name = genre.Name
				}
			}
			resp.Genres = append(resp.Genres, genreBase)
		}
	}

	// franchise
	if franchise, ok := franchiseMap[showModel.FranchiseID]; ok {
		resp.Franchise = &showDto.FranchiseBase{}
		resp.Franchise.Id = showModel.FranchiseID
		if name, ok := i18nKeyMap[franchise.NameKey]; ok {
			resp.Franchise.Name = name
		} else {
			resp.Franchise.Name = franchise.Name
		}
	}
	// credits
	for _, credit := range credits {
		creditBase := &showDto.CreditBase{
			Id:         credit.ID,
			Job:        uint32(credit.Job),
			Department: credit.Department,
			Character:  credit.Character,
			Order:      credit.Order,
		}
		creditBase.PersonBase = &showDto.PersonBase{}
		if person, ok := personMap[credit.PersonID]; ok {
			if name, ok := i18nKeyMap[person.NameKey]; ok {
				creditBase.Name = name
			}
			creditBase.OriginalName = person.Name
			creditBase.PersonID = person.ID
			creditBase.Gender = person.Gender
			if image, ok := imageMap[person.ProfileID]; ok {
				creditBase.ProfilePath = image.GetFilePath(channelKeyInfo.OssImageHost)
			}
		}

		if credit.Job == creditDao.CreditJobDirector {
			resp.CreatedBy = append(resp.CreatedBy, creditBase)
		}
		if credit.Job == creditDao.CreditJobActor {
			resp.Credits.Casts = append(resp.Credits.Casts, creditBase)
		} else {
			resp.Credits.Crews = append(resp.Credits.Crews, creditBase)
		}
	}

	sort.Slice(resp.CreatedBy, func(i, j int) bool {
		return resp.CreatedBy[i].Order < resp.CreatedBy[j].Order
	})
	// sort credits by order
	sort.Slice(resp.Credits.Casts, func(i, j int) bool {
		return resp.Credits.Casts[i].Order < resp.Credits.Casts[j].Order
	})
	sort.Slice(resp.Credits.Crews, func(i, j int) bool {
		return resp.Credits.Crews[i].Order < resp.Credits.Crews[j].Order
	})

	// seasons

	// episodes
	if episodes, ok := episodeShowMap[showModel.ID]; ok {
		//seasonBase.EpisodeCount = uint32(len(episodes))
		for _, episode := range episodes {
			episodeBase := &showDto.EpisodeBase{
				Id:            episode.ID,
				Name:          episode.Name,
				EpisodeNumber: episode.EpisodeNumber,
			}
			resp.Episodes = append(resp.Episodes, episodeBase)
		}
	}

	// posters
	for _, poster := range posters {
		if image, ok := imageMap[poster.ImageID]; ok {
			imageBase := &showDto.ImageBase{
				AspectRatio: image.AspectRatio,
				Height:      image.Height,
				Iso6391:     image.ISO_639_1,
				FilePath:    image.GetFilePath(channelKeyInfo.OssImageHost),
				Width:       image.Width,
				AspectType:  image.SetAspectType(),
			}
			resp.Images.Posters = append(resp.Images.Posters, imageBase)

			resp.Posters = append(resp.Posters, imageBase)
		}

	}
	return
}

// reorderShowListByIDs 根据向量搜索返回的ID顺序重新排序Show列表
func reorderShowListByIDs(showList []*showDto.ShowBase, orderedIDs []uint64) []*showDto.ShowBase {
	// 创建ID到索引的映射
	idToIndex := make(map[uint64]int, len(orderedIDs))
	for idx, id := range orderedIDs {
		idToIndex[id] = idx
	}

	// 创建ID到ShowBase的映射
	idToShow := make(map[uint64]*showDto.ShowBase, len(showList))
	for _, show := range showList {
		idToShow[show.ID] = show
	}

	// 按照orderedIDs的顺序重新组装结果
	result := make([]*showDto.ShowBase, 0, len(showList))
	for _, id := range orderedIDs {
		if show, exists := idToShow[id]; exists {
			result = append(result, show)
		}
	}

	return result
}

func (e Entry) saveSearchContent(ctx *gin.Context, req *showDto.ShowSearchReq) {
	var (
		model = &showDao.ShowSearchHistory{}
		t     []byte
		err   error
	)

	userInfo, _ := helper.GetCtxUser(ctx)
	if userInfo != nil {
		model.UserID = userInfo.UID
	}

	t, err = json.Marshal(req)
	if err != nil {
		log.Ctx(ctx).Error(err.Error())
	}
	if len(t) <= 0 {
		return
	}
	model.SearchContent = t
	model.Keyword = req.Keyword
	model.Iso6391 = req.ISO_639_1

	_, err = e.ShowRepo.SearchHistoryCreate(ctx, model)
	if err != nil {
		log.Ctx(ctx).Error(err.Error())
	}

	return
}

func (e Entry) ShowSearch(ctx *gin.Context, req *showDto.ShowSearchReq) (resp *showDto.ShowSearchResp, err error) {
	resp = &showDto.ShowSearchResp{}
	resp.ShowListResp = &showDto.ShowListResp{}
	resp.List = make([]*showDto.ShowBase, 0)

	if !strUtil.IsValidLength(req.Keyword, 500) {
		return nil, ecode.MoreThanMaxErr
	}

	// 验证请求参数
	reqCtx, err := e.validateShowListRequest(ctx)
	if err != nil {
		return nil, err
	}

	// 设置请求参数
	req.ChannelID = reqCtx.ChannelID
	req.VersionID = reqCtx.VersionID
	req.ISO_639_1 = reqCtx.ISO639_1

	//req.SortReq = e.setSort(req.FieldIDs)

	// 判断是否使用向量搜索
	var useVectorSearch bool
	var vectorShowIDs []uint64
	var vectorRequestID string

	if req.Keyword != "" && shouldUseVectorSearchForShow(ctx, req) {
		log.Ctx(ctx).WithFields(logrus.Fields{
			"useVectorSearch": true,
		}).Debug("useVectorSearch")
		useVectorSearch = true
		req.VectorTopK = req.Limit
		// 设置向量搜索的默认参数
		setVectorSearchDefaultsForShow(req)

		// 使用增强搜索服务执行向量搜索
		if e.SearchApi != nil {
			vectorShowIDs, vectorRequestID, err = e.EnhancedShowSearchV2(ctx, req)
		} else {
			// 如果 SearchApi 未初始化，直接回退到传统搜索
			useVectorSearch = false
		}
		if err != nil || len(vectorShowIDs) == 0 {
			// 向量搜索失败或无结果，回退到传统搜索
			useVectorSearch = false
		}
		// 如果有 RequestID，可以添加到响应中（需要先在 ShowSearchResp 中添加该字段）
		_ = vectorRequestID
	}

	// 设置过滤器
	filter := &showDao.Filter{
		ChannelID: req.ChannelID,
		Status:    uint32(dbs.StatusEnable),
		FieldIDs:  req.FieldIDs,
	}

	// 根据搜索模式设置过滤条件
	if useVectorSearch && len(vectorShowIDs) > 0 {
		// 使用向量搜索结果
		filter.IDs = vectorShowIDs
	} else if req.Keyword != "" {
		// 使用传统的关键词搜索
		filter.Keyword = req.Keyword
	}

	// 应用版本过滤逻辑
	_, err = e.applyVersionFilter(ctx, reqCtx, filter)
	if err != nil {
		return nil, err
	}
	// 如果版本已下线，直接返回空结果
	if reqCtx.VersionModel.Status == uint32(dbs.StatusDisable) {
		return resp, nil
	}

	e.buildFilter(ctx, req, filter)

	newCtx := ctx.Copy()
	newCtx.Request = newCtx.Request.Clone(context.WithoutCancel(newCtx.Request.Context()))

	go e.saveSearchContent(newCtx, req)

	cnt, list, err := e.ShowRepo.DataPageList(ctx, filter,
		req.Page, req.Limit)
	if err != nil {
		return nil, err
	}
	if cnt == 0 {
		return
	}
	resp.Count = cnt
	if len(list) <= 0 {
		return resp, nil
	}

	// 构建公共关联数据
	commonData, err := e.buildShowListCommonData(ctx, list, req.ISO_639_1, true)
	if err != nil {
		return nil, err
	}

	// 构建响应
	e.setShowListResp(
		resp.ShowListResp,
		list,
		commonData.Posters,
		commonData.Images,
		commonData.Genres,
		commonData.WithGenres,
		commonData.Franchises,
		commonData.I18nKeyMap,
		reqCtx.ChannelKeyInfo,
	)

	// 获取剧集数量信息
	showEpisodeMap, err := e.getShowEpisodeInfo(ctx, commonData.ShowIDs)
	if err != nil {
		return resp, nil
	}
	if len(showEpisodeMap) > 0 {
		for _, show := range resp.List {
			if episodeCount, ok := showEpisodeMap[show.ID]; ok {
				show.NumberOfEpisodes = uint32(episodeCount)
			}
		}
	}

	// 处理分类信息
	if len(commonData.FieldMap) > 0 {
		for _, show := range resp.List {
			if show.Classes == nil || len(show.Classes) <= 0 {
				show.Classes = make([]*showDto.ClassBase, 0)
			}
			if withs, ok := commonData.WithFieldMap[show.ID]; ok {
				for _, with := range withs {
					if fields, ok := commonData.FieldMap[with.FieldID]; ok {
						for _, field := range fields {
							class := &showDto.ClassBase{
								Id:      field.ID,
								ClassID: field.ClassID,
								Name:    field.Name,
							}

							if name, ok := commonData.I18nKeyMap[field.NameKey]; ok {
								class.Name = name
							}

							show.Classes = append(show.Classes, class)
						}
					}
				}
			}
		}
	}

	// 如果使用了向量搜索，需要按照向量搜索返回的ID顺序重新排序结果
	if useVectorSearch && len(vectorShowIDs) > 0 {
		resp.List = reorderShowListByIDs(resp.List, vectorShowIDs)
	}

	return resp, nil
}

func (e Entry) ShowRecommendList(ctx *gin.Context, req *showDto.ShowRecommendListReq) (resp *showDto.ShowRecommendListResp, err error) {
	resp = &showDto.ShowRecommendListResp{}
	resp.List = make([]*showDto.ShowRecommendListItem, 0)

	// 验证请求参数
	reqCtx, err := e.validateShowListRequest(ctx)
	if err != nil {
		return nil, err
	}

	// 设置请求参数
	req.ChannelID = reqCtx.ChannelID
	req.VersionID = reqCtx.VersionID
	req.ISO_639_1 = reqCtx.ISO639_1

	// 设置过滤器
	var (
		showList = make(showDao.ShowList, 0)
	)

	filter := &showDao.Filter{
		ChannelID: req.ChannelID,
	}

	recFilter := &recommendDao.Filter{
		Status: uint32(dbs.StatusEnable),
		Sort: []clause.OrderByColumn{
			{
				Column: clause.Column{
					Name: "order"}, // order 顺序
				Desc: false,
			},
		},
		ChannelID: req.ChannelID,
	}

	// 应用版本过滤逻辑
	pluckShowIDs, err := e.applyVersionFilter(ctx, reqCtx, filter)
	if err != nil {
		return nil, err
	}
	// 如果版本已下线，直接返回空结果
	if reqCtx.VersionModel.Status == uint32(dbs.StatusDisable) {
		return resp, nil
	}
	// 如果在审核中且有限制的 show IDs，设置到推荐过滤器中
	if len(pluckShowIDs) > 0 {
		recFilter.ShowIDs = pluckShowIDs
	}

	cnt, list, err := e.RecommendRepo.DataPageList(ctx, recFilter, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}
	if len(list) <= 0 {
		return resp, nil
	}

	showList, err = e.ShowRepo.FindByFilter(ctx, &showDao.Filter{
		IDs: list.GetShowIDs(),
	})
	if err != nil {
		return nil, err
	}

	if len(showList) <= 0 {
		return resp, nil
	}

	resp.Count = cnt

	// 构建公共关联数据
	commonData, err := e.buildShowListCommonData(ctx, showList, req.ISO_639_1, false)
	if err != nil {
		return nil, err
	}

	// 添加推荐数据的 i18n keys
	commonData.I18nKeyList = append(commonData.I18nKeyList, list.GetKeys()...)
	// 添加推荐数据的 image IDs
	commonData.ImageIDs = append(commonData.ImageIDs, list.GetImageIDs()...)

	// 重新查询 images 和 i18n（因为添加了新的数据）
	if len(commonData.ImageIDs) > 0 {
		commonData.Images, err = e.ImageRepo.FindByFilter(ctx, &imageDao.Filter{
			IDs: commonData.ImageIDs,
		})
		if err != nil {
			return nil, err
		}
	}

	if len(commonData.I18nKeyList) > 0 {
		commonData.I18nKeys, err = e.I18nRepo.FindByFilterWithFallback(ctx, &i18nDao.Filter{
			Keys:      commonData.I18nKeyList,
			ISO_639_1: req.ISO_639_1,
		}, "en_US")
		if err != nil {
			return nil, err
		}
	}

	commonData.I18nKeyMap = commonData.I18nKeys.GetI18nKeyMap()

	// 构建基础的 show 列表响应
	var (
		showListResp = &showDto.ShowListResp{
			List: make([]*showDto.ShowBase, 0),
		}
		showListMap = make(map[uint64]*showDto.ShowBase)
		imageMap    = commonData.Images.GetMap()
	)

	e.setShowListResp(
		showListResp,
		showList,
		commonData.Posters,
		commonData.Images,
		commonData.Genres,
		commonData.WithGenres,
		commonData.Franchises,
		commonData.I18nKeyMap,
		reqCtx.ChannelKeyInfo,
	)

	for _, base := range showListResp.List {
		showListMap[base.ID] = base
	}

	// 获取剧集数量信息
	showEpisodeMap, err := e.getShowEpisodeInfo(ctx, commonData.ShowIDs)
	if err != nil {
		// 继续处理，即使获取剧集数量失败
		showEpisodeMap = make(map[uint64]int64)
	}
	// 更新 showListMap 中的剧集数量
	if len(showEpisodeMap) > 0 {
		for showID, episodeCount := range showEpisodeMap {
			if show, ok := showListMap[showID]; ok {
				show.NumberOfEpisodes = uint32(episodeCount)
			}
		}
	}

	for _, rec := range list {
		if showBase, ok := showListMap[rec.ShowID]; ok {
			item := &showDto.ShowRecommendListItem{
				ShowBase:  showBase,
				Recommend: &showDto.RecommendBase{},
			}

			if n, ok := commonData.I18nKeyMap[rec.NameKey]; ok {
				item.Recommend.Name = n
			}
			if i, ok := imageMap[rec.ImageID]; ok {
				item.Recommend.Image = &showDto.ImageBase{
					AspectRatio: i.AspectRatio,
					Height:      i.Height,
					Iso6391:     i.ISO_639_1,
					FilePath:    i.GetFilePath(reqCtx.ChannelKeyInfo.OssImageHost),
					Width:       i.Width,
					AspectType:  i.SetAspectType(),
				}
			}
			resp.List = append(resp.List, item)
		}
	}

	return resp, nil
}

func (e Entry) ShowAssignList(ctx *gin.Context, req *showDto.ShowAssignListReq) (resp *showDto.ShowAssignListResp, err error) {
	resp = &showDto.ShowAssignListResp{}
	resp.ShowListResp = &showDto.ShowListResp{}
	resp.List = make([]*showDto.ShowBase, 0)

	// 验证请求参数
	reqCtx, err := e.validateShowListRequest(ctx)
	if err != nil {
		return nil, err
	}

	// 设置请求参数
	req.ChannelID = reqCtx.ChannelID
	req.VersionID = reqCtx.VersionID
	req.ISO_639_1 = reqCtx.ISO639_1

	// 设置过滤器
	var (
		showList = make(showDao.ShowList, 0)
	)

	filter := &showDao.Filter{
		ChannelID: req.ChannelID,
	}

	assignFilter := &assignDao.Filter{
		Status:    uint32(dbs.StatusEnable),
		ChannelID: req.ChannelID,
	}

	// 应用版本过滤逻辑
	pluckShowIDs, err := e.applyVersionFilter(ctx, reqCtx, filter)
	if err != nil {
		return nil, err
	}
	// 如果版本已下线，直接返回空结果
	if reqCtx.VersionModel.Status == uint32(dbs.StatusDisable) {
		return resp, nil
	}
	// 如果在审核中且有限制的 show IDs，设置到分配过滤器中
	if len(pluckShowIDs) > 0 {
		assignFilter.ShowIDs = pluckShowIDs
	}

	cnt, list, err := e.AssignRepo.DataPageList(ctx, assignFilter, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}
	if cnt == 0 {
		return
	}

	if len(list) <= 0 {
		return resp, nil
	}

	showList, err = e.ShowRepo.FindByFilter(ctx, &showDao.Filter{
		IDs: list.GetShowIDs(),
	})
	if err != nil {
		return nil, err
	}

	if len(showList) <= 0 {
		return resp, nil
	}

	resp.Count = cnt

	// 构建公共关联数据
	commonData, err := e.buildShowListCommonData(ctx, showList, req.ISO_639_1, false)
	if err != nil {
		return nil, err
	}

	// 构建响应
	resp.ShowListResp = &showDto.ShowListResp{
		Count: cnt,
	}
	e.setShowListResp(
		resp.ShowListResp,
		showList,
		commonData.Posters,
		commonData.Images,
		commonData.Genres,
		commonData.WithGenres,
		commonData.Franchises,
		commonData.I18nKeyMap,
		reqCtx.ChannelKeyInfo,
	)

	// 获取剧集数量信息
	showEpisodeMap, err := e.getShowEpisodeInfo(ctx, commonData.ShowIDs)
	if err != nil {
		return resp, nil
	}
	if len(showEpisodeMap) > 0 {
		for _, show := range resp.List {
			if episodeCount, ok := showEpisodeMap[show.ID]; ok {
				show.NumberOfEpisodes = uint32(episodeCount)
			}
		}
	}

	return
}

func (e Entry) ShowPopularList(ctx *gin.Context, req *showDto.ShowPopularListReq) (resp *showDto.ShowPopularListResp, err error) {
	resp = &showDto.ShowPopularListResp{}
	resp.ShowListResp = &showDto.ShowListResp{}
	resp.List = make([]*showDto.ShowBase, 0)

	// 验证请求参数
	reqCtx, err := e.validateShowListRequest(ctx)
	if err != nil {
		return nil, err
	}

	// 设置请求参数
	req.ChannelID = reqCtx.ChannelID
	req.VersionID = reqCtx.VersionID
	req.ISO_639_1 = reqCtx.ISO639_1

	// 设置过滤器
	var (
		showList = make(showDao.ShowList, 0)
	)

	filter := &showDao.Filter{
		ChannelID: req.ChannelID,
	}

	popFilter := &popularDao.Filter{
		Status:    uint32(dbs.StatusEnable),
		ChannelID: req.ChannelID,
	}

	// 应用版本过滤逻辑
	pluckShowIDs, err := e.applyVersionFilter(ctx, reqCtx, filter)
	if err != nil {
		return nil, err
	}
	// 如果版本已下线，直接返回空结果
	if reqCtx.VersionModel.Status == uint32(dbs.StatusDisable) {
		return resp, nil
	}
	// 如果在审核中且有限制的 show IDs，设置到热门过滤器中
	if len(pluckShowIDs) > 0 {
		popFilter.ShowIDs = pluckShowIDs
	}

	cnt, list, err := e.PopularRepo.DataPageList(ctx, popFilter, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}
	if cnt == 0 {
		return
	}

	if len(list) <= 0 {
		return resp, nil
	}

	showList, err = e.ShowRepo.FindByFilter(ctx, &showDao.Filter{
		IDs: list.GetShowIDs(),
	})
	if err != nil {
		return nil, err
	}

	if len(showList) <= 0 {
		return resp, nil
	}

	resp.Count = cnt

	// 构建公共关联数据
	commonData, err := e.buildShowListCommonData(ctx, showList, req.ISO_639_1, false)
	if err != nil {
		return nil, err
	}

	// 构建响应
	resp.ShowListResp = &showDto.ShowListResp{
		Count: cnt,
	}
	e.setShowListResp(
		resp.ShowListResp,
		showList,
		commonData.Posters,
		commonData.Images,
		commonData.Genres,
		commonData.WithGenres,
		commonData.Franchises,
		commonData.I18nKeyMap,
		reqCtx.ChannelKeyInfo,
	)

	// 获取剧集数量信息
	showEpisodeMap, err := e.getShowEpisodeInfo(ctx, commonData.ShowIDs)
	if err != nil {
		return resp, nil
	}
	if len(showEpisodeMap) > 0 {
		for _, show := range resp.List {
			if episodeCount, ok := showEpisodeMap[show.ID]; ok {
				show.NumberOfEpisodes = uint32(episodeCount)
			}
		}
	}

	return
}

// 计算每个show下面有多少episode
func (e Entry) getAdminShowEpisodeInfo(ctx *gin.Context, showIDs []uint64) (map[uint64]int64, error) {
	var (
		episodeMap = make(map[uint64]int64)
		err        error
	)

	episodeMap, err = e.EpisodeRepo.BatchCountByFilter(ctx, &episodeDao.Filter{
		ShowIDs: showIDs,
	})
	if err != nil {
		return nil, err
	}

	return episodeMap, nil
}

func (e Entry) getShowEpisodeInfo(ctx *gin.Context, showIDs []uint64) (map[uint64]int64, error) {
	var (
		episodeMap = make(map[uint64]int64)
		err        error
	)

	episodeMap, err = e.EpisodeRepo.BatchCountByFilter(ctx, &episodeDao.Filter{
		ShowIDs: showIDs,
		Status:  uint32(dbs.StatusEnable),
	})
	if err != nil {
		return nil, err
	}

	return episodeMap, nil
}

/*
请求频道列表, 获取各个频道的id

根据频道数作并发操作, 每个频道作为一个独立协程处理

协程内通过分页调用视频列表, 获取每个视频的id, 再并发处理, 根据id获取每个视频的详情和播放, 处理入库逻辑
*/
//
//func (e *Entry) getTagNameCheck(ctx *gin.Context, tx *gorm.DB, groupId uint64, tags goods_tag_param.ModelList) (map[string]uint64, error) {
//	ret := make(map[string]uint64)
//	/*
//		传入tag的name列表
//		1. 已经存在的
//			[]model
//		2. 需要新建的
//			[]model
//		都拿到了ID, 合并到一起, 生成map
//	*/
//	list := tags.GetNameList(groupId)
//	list = lo.Uniq(list)
//	existList, err := e.TagParamRepo.FindByFilter(ctx, &goods_tag_param.Filter{
//		GroupID:       groupId,
//		NameCheckList: list,
//		Enable:        dbs.StatusEnable,
//	})
//	if err != nil {
//		return ret, err
//	}
//	// 找到 tags.GetNameList() 和 existList的补集
//	existMap := existList.GetNameMap()
//	newList := make([]string, 0)
//	newTags := make([]*goods_tag_param.Model, 0)
//	tagUniq := make(map[goods_tag_param.Model]struct{})
//
//	for _, name := range list {
//		if _, ok := existMap[name]; !ok {
//			newList = append(newList, name)
//			item := goods_tag_param.Model{
//				GroupID:   groupId,
//				GroupName: goods_tag_group.GroupMap[groupId],
//				Name:      name,
//				Enable:    dbs.StatusEnable,
//			}
//			if _, ok := tagUniq[item]; !ok {
//				tagUniq[item] = struct{}{}
//				newTags = append(newTags, &item)
//			}
//		}
//	}
//	if len(newTags) > 0 {
//		err = e.TagParamRepo.BatchCreateWithTx(ctx, tx, newTags)
//		if err != nil {
//			return ret, err
//		}
//	}
//	// 合并
//	for _, model := range existMap {
//		if _, ok := ret[model.Name]; !ok {
//			ret[model.Name] = model.ID
//		}
//	}
//	for _, model := range newTags {
//		if _, ok := ret[model.Name]; !ok {
//			ret[model.Name] = model.ID
//		}
//	}
//	return ret, nil
//}

// ExportShowsWithI18n 导出所有show记录的多语言JSON数据（内存版本，适用于小数据量）
func (e Entry) ExportShowsWithI18n(ctx *gin.Context, req *showDto.ShowI18nExportReq) (*showDto.ShowI18nExportResp, error) {
	resp := &showDto.ShowI18nExportResp{
		List: make([]*showDto.ShowI18nExport, 0),
	}

	// 构建过滤器
	filter := &showDao.Filter{}
	if req.Status != nil {
		filter.Status = *req.Status
	}

	// 查询所有show记录
	showList, err := e.ShowRepo.FindByFilter(ctx, filter)
	if err != nil {
		return nil, err
	}

	if len(showList) == 0 {
		resp.Total = 0
		return resp, nil
	}

	resp.Total = int64(len(showList))

	// 收集所有的i18n keys
	i18nKeyList := make([]string, 0)
	for _, show := range showList {
		if show.NameKey != "" {
			i18nKeyList = append(i18nKeyList, show.NameKey)
		}
		if show.OverviewKey != "" {
			i18nKeyList = append(i18nKeyList, show.OverviewKey)
		}
	}

	// 查询所有语言版本的i18n数据（不限制语言）
	var i18nKeys i18nDao.I18nList
	if len(i18nKeyList) > 0 {
		i18nKeys, err = e.I18nRepo.FindByFilter(ctx, &i18nDao.Filter{
			Keys: i18nKeyList,
			// 不设置ISO_639_1，获取所有语言版本
		})
		if err != nil {
			return nil, err
		}
	}

	// 获取按key分组的翻译值数组
	i18nValueArrays := i18nKeys.GetI18nValueArrays(req.IncludeEmpty)

	// 组装响应数据
	for _, show := range showList {
		exportItem := &showDto.ShowI18nExport{
			ID:           show.ID,
			Name:         show.Name,
			Overview:     show.Overview,
			NameI18n:     make([]string, 0),
			OverviewI18n: make([]string, 0),
		}

		// 获取name的多语言版本
		if show.NameKey != "" {
			if nameValues, exists := i18nValueArrays[show.NameKey]; exists {
				exportItem.NameI18n = nameValues
			}
		}

		// 获取overview的多语言版本
		if show.OverviewKey != "" {
			if overviewValues, exists := i18nValueArrays[show.OverviewKey]; exists {
				exportItem.OverviewI18n = overviewValues
			}
		}

		resp.List = append(resp.List, exportItem)
	}

	return resp, nil
}

// ExportShowsWithI18nToFile 高性能导出show记录到JSON文件（支持大数据量）
func (e Entry) ExportShowsWithI18nToFile(ctx *gin.Context, config *showDto.ShowI18nExportConfig) error {
	// 设置默认值
	if config.BatchSize <= 0 {
		config.BatchSize = 1000
	}
	if config.OutputPath == "" {
		config.OutputPath = "shows_i18n_export.json"
	}

	// 构建过滤器
	filter := &showDao.Filter{}
	if config.Status != nil {
		filter.Status = *config.Status
	}

	// 先查询总记录数
	totalCount, err := e.ShowRepo.CountByFilter(ctx, filter)
	if err != nil {
		return err
	}

	if totalCount == 0 {
		if config.ProgressFunc != nil {
			config.ProgressFunc(0, 0, "没有找到任何show记录")
		}
		return nil
	}

	if config.ProgressFunc != nil {
		config.ProgressFunc(0, totalCount, fmt.Sprintf("开始导出，共 %d 条记录", totalCount))
	}

	// 创建输出文件
	file, err := os.Create(config.OutputPath)
	if err != nil {
		return fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	// 使用缓冲写入
	writer := bufio.NewWriter(file)
	defer writer.Flush()

	// 写入JSON数组开始
	_, err = writer.WriteString("[\n")
	if err != nil {
		return err
	}

	var processedCount int64
	isFirstItem := true

	// 分页查询和处理
	for offset := 0; int64(offset) < totalCount; offset += config.BatchSize {
		// 查询当前批次的show记录
		_, showList, err := e.ShowRepo.DataPageList(ctx, filter, offset/config.BatchSize+1, config.BatchSize)
		if err != nil {
			return err
		}

		if len(showList) == 0 {
			break
		}

		// 处理当前批次
		err = e.processBatchToFile(ctx, showList, writer, &isFirstItem, config)
		if err != nil {
			return err
		}

		processedCount += int64(len(showList))

		if config.ProgressFunc != nil {
			progress := float64(processedCount) / float64(totalCount) * 100
			config.ProgressFunc(processedCount, totalCount,
				fmt.Sprintf("已处理 %d/%d 条记录 (%.1f%%)", processedCount, totalCount, progress))
		}
	}

	// 写入JSON数组结束
	_, err = writer.WriteString("\n]")
	if err != nil {
		return err
	}

	if config.ProgressFunc != nil {
		config.ProgressFunc(totalCount, totalCount, "导出完成")
	}

	return nil
}

// processBatchToFile 处理一批show记录并写入文件
func (e Entry) processBatchToFile(ctx *gin.Context, showList showDao.ShowList, writer *bufio.Writer, isFirstItem *bool, config *showDto.ShowI18nExportConfig) error {
	// 收集当前批次的show IDs
	showIDs := showList.GetIDs()

	// 查询当前批次的ShowWithClass关联数据
	showWithClassList, err := e.ShowRepo.FindClassByFilter(ctx, &showDao.ClassFilter{
		ShowIDs: showIDs,
	})
	if err != nil {
		return err
	}

	// 按ShowID分组ShowWithClass数据
	showWithClassMap := showWithClassList.GetShowIDMap()

	// 收集field IDs
	fieldIDs := showWithClassList.GetFieldIDs()

	// 查询content_class_field表获取字段信息
	var classFieldList classFieldDao.ModelList
	if len(fieldIDs) > 0 {
		classFieldList, err = e.ClassFieldRepo.FindByFilter(ctx, &classFieldDao.Filter{
			IDs: fieldIDs,
		})
		if err != nil {
			return err
		}
	}

	// 构建field ID到Model的映射
	classFieldMap := classFieldList.GetMap()

	// 收集当前批次的i18n keys
	i18nKeyList := make([]string, 0)
	for _, show := range showList {
		if show.NameKey != "" {
			i18nKeyList = append(i18nKeyList, show.NameKey)
		}
		if show.OverviewKey != "" {
			i18nKeyList = append(i18nKeyList, show.OverviewKey)
		}
	}

	// 收集标量字段的name_key
	for _, field := range classFieldList {
		if field.NameKey != "" {
			i18nKeyList = append(i18nKeyList, field.NameKey)
		}
	}

	// 查询当前批次的i18n数据
	var i18nKeys i18nDao.I18nList
	if len(i18nKeyList) > 0 {
		i18nKeys, err = e.I18nRepo.FindByFilter(ctx, &i18nDao.Filter{
			Keys: i18nKeyList,
		})
		if err != nil {
			return err
		}
	}

	// 获取按key分组的翻译值数组
	i18nValueArrays := i18nKeys.GetI18nValueArrays(config.IncludeEmpty)

	// 处理每个show记录
	for _, show := range showList {
		exportItem := &showDto.ShowI18nExport{
			ID:           show.ID,
			Name:         show.Name,
			Overview:     show.Overview,
			Status:       show.Status,
			Score:        show.Score,
			ContentType:  uint32(show.ContentType),
			NameI18n:     make([]string, 0),
			OverviewI18n: make([]string, 0),
			RegionI18n:   make([]string, 0),
			RegionIDs:    make([]uint64, 0),
			YearI18n:     make([]string, 0),
			YearIDs:      make([]uint64, 0),
			GenreI18n:    make([]string, 0),
			GenreIDs:     make([]uint64, 0),
		}

		// 获取name的多语言版本
		if show.NameKey != "" {
			if nameValues, exists := i18nValueArrays[show.NameKey]; exists {
				exportItem.NameI18n = nameValues
			}
		}

		// 获取overview的多语言版本
		if show.OverviewKey != "" {
			if overviewValues, exists := i18nValueArrays[show.OverviewKey]; exists {
				exportItem.OverviewI18n = overviewValues
			}
		}

		// 处理标量字段
		if showClassList, exists := showWithClassMap[show.ID]; exists {
			for _, showClass := range showClassList {
				if field, exists := classFieldMap[showClass.FieldID]; exists {
					switch showClass.ClassID {
					case 2: // 地区
						// 如果是第一个地区，设置为主要地区
						if exportItem.Region == "" {
							exportItem.Region = field.Name
						}
						// 添加到地区ID列表
						exportItem.RegionIDs = append(exportItem.RegionIDs, field.ID)
						if field.NameKey != "" {
							if regionValues, exists := i18nValueArrays[field.NameKey]; exists {
								exportItem.RegionI18n = regionValues
							}
						}
					case 3: // 年份
						// 如果是第一个年份，设置为主要年份
						if exportItem.Year == "" {
							exportItem.Year = field.Name
						}
						// 添加到年份ID列表
						exportItem.YearIDs = append(exportItem.YearIDs, field.ID)
						if field.NameKey != "" {
							if yearValues, exists := i18nValueArrays[field.NameKey]; exists {
								exportItem.YearI18n = yearValues
							}
						}
					case 5: // 类型
						// 如果是第一个类型，设置为主要类型
						if exportItem.Genre == "" {
							exportItem.Genre = field.Name
						}
						// 添加到类型ID列表
						exportItem.GenreIDs = append(exportItem.GenreIDs, field.ID)
						if field.NameKey != "" {
							if genreValues, exists := i18nValueArrays[field.NameKey]; exists {
								exportItem.GenreI18n = genreValues
							}
						}
					}
				}
			}
		}

		// 序列化为JSON
		jsonData, err := json.Marshal(exportItem)
		if err != nil {
			return err
		}

		// 写入文件
		if !*isFirstItem {
			_, err = writer.WriteString(",\n")
			if err != nil {
				return err
			}
		} else {
			*isFirstItem = false
		}

		_, err = writer.WriteString("  ")
		if err != nil {
			return err
		}

		_, err = writer.Write(jsonData)
		if err != nil {
			return err
		}
	}

	return nil
}

// EnhancedShowSearchV2 增强的节目搜索（返回ID列表）
// 优先使用向量搜索，如果向量搜索不可用，调用者可以回退到 MySQL 查询
func (e *Entry) EnhancedShowSearchV2(ctx *gin.Context, req *showDto.ShowSearchReq) ([]uint64, string, error) {
	// 如果没有搜索关键词，返回空
	if req.Keyword == "" {
		return nil, "", nil
	}

	// 检查searchDao是否可用
	if e.SearchApi == nil {
		log.Ctx(ctx).Warn("SearchDao is not available, falling back to MySQL search")
		return nil, "", nil
	}

	// 构建向量搜索请求
	vectorReq := e.buildVectorSearchRequest(req)

	// 执行向量搜索
	vectorResults, err := e.performVectorSearch(ctx, vectorReq)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("Show向量搜索失败")
		return nil, "", err
	}

	// 如果向量搜索没有结果，返回空
	if len(vectorResults.Items) == 0 {
		log.Ctx(ctx).Info("Show向量搜索无结果，可能需要回退到MySQL查询")
		return nil, "", nil
	}

	// 提取show IDs
	showIDs := make([]uint64, 0, len(vectorResults.Items))
	for _, item := range vectorResults.Items {
		if item.ID > 0 {
			showIDs = append(showIDs, item.ID)
		}
	}

	// 返回RequestID
	requestID := vectorResults.SearchInfo.VectorRequestID

	return showIDs, requestID, nil
}

// EnhancedShowSearch 增强的节目搜索，集成向量搜索和传统搜索（保留原方法以兼容）
func (e *Entry) EnhancedShowSearch(ctx *gin.Context, req *showDto.ShowSearchReq) (*showDto.ShowSearchResp, error) {
	// 如果没有关键词或者明确不使用向量搜索，则使用传统搜索
	if req.Keyword == "" || !req.UseVector {
		return e.ShowSearch(ctx, req)
	}

	// 设置向量搜索默认值
	e.setVectorSearchDefaults(req)

	// 构建向量搜索请求
	vectorReq := e.buildVectorSearchRequest(req)

	// 执行向量搜索
	vectorResults, err := e.performVectorSearch(ctx, vectorReq)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("向量搜索失败，回退到传统搜索")
		return e.ShowSearch(ctx, req)
	}

	// 如果向量搜索没有结果，回退到传统搜索
	if len(vectorResults.Items) == 0 {
		log.Ctx(ctx).Info("向量搜索无结果，回退到传统搜索")
		return e.ShowSearch(ctx, req)
	}

	// 将向量搜索结果转换为传统搜索结果格式
	resp, err := e.convertVectorResultsToShowResponse(ctx, vectorResults, req)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("向量搜索结果转换失败，回退到传统搜索")
		return e.ShowSearch(ctx, req)
	}

	// 保存搜索记录（复用原有逻辑）
	newCtx := ctx.Copy()
	newCtx.Request = newCtx.Request.Clone(context.WithoutCancel(newCtx.Request.Context()))

	go e.saveSearchContent(newCtx, req)

	return resp, nil
}

// setVectorSearchDefaults 设置向量搜索默认值
func (e *Entry) setVectorSearchDefaults(req *showDto.ShowSearchReq) {
	if req.DenseWeight <= 0 {
		req.DenseWeight = 0.5
	}
	if req.SparseWeight <= 0 {
		req.SparseWeight = 0.5
	}
	if req.VectorTopK <= 0 {
		req.VectorTopK = 100
	}
}

// buildVectorSearchRequest 构建向量搜索请求
func (e *Entry) buildVectorSearchRequest(req *showDto.ShowSearchReq) *dto.SearchRequest {
	// 构建筛选条件
	filters := dto.SearchFilters{}

	// 从FieldIDs推断分类（简化处理）
	if len(req.FieldIDs) > 0 {
		//// 这里需要根据实际的FieldID到Category的映射逻辑来实现
		//// 现在先简单处理
		//filters.Categories = e.mapFieldIDsToCategories(req.FieldIDs)
	}

	// 从GenreID推断类型
	if req.GenreID > 0 {
		//// 这里需要根据实际的GenreID到Genre名称的映射来实现
		//genreName := e.mapGenreIDToName(req.GenreID)
		//if genreName != "" {
		//	filters.Genres = []string{genreName}
		//}
	}

	filters.Status = []uint32{
		uint32(dbs.StatusEnable),
	}

	return &dto.SearchRequest{
		Query:      req.Keyword,
		SearchMode: "hybrid", // 使用混合搜索模式
		Language:   req.ISO_639_1,
		Page:       req.Page,
		PageSize:   req.Limit,
		SortBy:     e.mapSortType(req.SortReq.Type),
		SortOrder:  string(req.SortReq.SortMethod),
		Filters:    filters,
		VectorParams: dto.VectorSearchParams{
			DenseWeight:  req.DenseWeight,
			SparseWeight: req.SparseWeight,
			TopK:         req.VectorTopK,
		},
	}
}

// performVectorSearch 执行向量搜索
func (e *Entry) performVectorSearch(ctx *gin.Context, req *dto.SearchRequest) (*dto.SearchResponse, error) {
	return e.SearchApi.Search(ctx, req)
}

// convertVectorResultsToShowResponse 将向量搜索结果转换为传统搜索结果格式
func (e *Entry) convertVectorResultsToShowResponse(ctx *gin.Context, vectorResults *dto.SearchResponse, originalReq *showDto.ShowSearchReq) (*showDto.ShowSearchResp, error) {
	resp := &showDto.ShowSearchResp{
		ShowListResp: &showDto.ShowListResp{
			List:  make([]*showDto.ShowBase, 0),
			Count: vectorResults.Total,
		},
	}

	if len(vectorResults.Items) == 0 {
		return resp, nil
	}

	// 提取ShowID列表
	showIDs := make([]uint64, 0, len(vectorResults.Items))
	scoreMap := make(map[uint64]float64) // 存储相关度分数

	for _, item := range vectorResults.Items {
		if item.ShowID > 0 {
			showIDs = append(showIDs, item.ShowID)
			scoreMap[item.ShowID] = item.Score
		}
	}

	// 如果没有有效的ShowID，返回空结果
	if len(showIDs) == 0 {
		return resp, nil
	}

	// 使用传统服务获取完整的节目信息
	showDetails, err := e.getShowDetailsByIDs(ctx, showIDs, originalReq)
	if err != nil {
		return nil, err
	}

	// 按向量搜索的相关度排序结果
	orderedShows := e.orderShowsByVectorScore(showDetails, scoreMap, showIDs)

	resp.List = orderedShows
	resp.Count = int64(len(orderedShows))

	return resp, nil
}

// getShowDetailsByIDs 根据ShowID列表获取完整的节目信息
func (e *Entry) getShowDetailsByIDs(ctx *gin.Context, showIDs []uint64, originalReq *showDto.ShowSearchReq) ([]*showDto.ShowBase, error) {
	// 验证请求参数（复用原服务逻辑）
	reqCtx, err := e.validateShowListRequest(ctx)
	if err != nil {
		return nil, err
	}

	// 构建查询条件
	filter := &showDao.Filter{
		IDs:       showIDs,
		ChannelID: reqCtx.ChannelID,
		Status:    uint32(dbs.StatusEnable),
	}

	// 应用版本过滤逻辑
	_, err = e.applyVersionFilter(ctx, reqCtx, filter)
	if err != nil {
		return nil, err
	}

	// 查询节目列表
	_, showList, err := e.ShowRepo.DataPageList(ctx, filter, 1, len(showIDs))
	if err != nil {
		return nil, err
	}

	if len(showList) == 0 {
		return []*showDto.ShowBase{}, nil
	}

	// 构建公共关联数据
	commonData, err := e.buildShowListCommonData(ctx, showList, originalReq.ISO_639_1, true)
	if err != nil {
		return nil, err
	}

	// 构建响应结构
	tempResp := &showDto.ShowListResp{
		List: make([]*showDto.ShowBase, 0),
	}

	e.setShowListResp(
		tempResp,
		showList,
		commonData.Posters,
		commonData.Images,
		commonData.Genres,
		commonData.WithGenres,
		commonData.Franchises,
		commonData.I18nKeyMap,
		reqCtx.ChannelKeyInfo,
	)

	// 处理分类信息（复用原服务逻辑）
	if len(commonData.FieldMap) > 0 {
		for _, show := range tempResp.List {
			if show.Classes == nil || len(show.Classes) <= 0 {
				show.Classes = make([]*showDto.ClassBase, 0)
			}
			if withs, ok := commonData.WithFieldMap[show.ID]; ok {
				for _, with := range withs {
					if fields, ok := commonData.FieldMap[with.FieldID]; ok {
						for _, field := range fields {
							class := &showDto.ClassBase{
								Id:      field.ID,
								ClassID: field.ClassID,
								Name:    field.Name,
							}

							if name, ok := commonData.I18nKeyMap[field.NameKey]; ok {
								class.Name = name
							}

							show.Classes = append(show.Classes, class)
						}
					}
				}
			}
		}
	}

	return tempResp.List, nil
}

// orderShowsByVectorScore 按向量搜索相关度分数排序节目
func (e *Entry) orderShowsByVectorScore(shows []*showDto.ShowBase, scoreMap map[uint64]float64, orderedIDs []uint64) []*showDto.ShowBase {
	// 创建ShowID到ShowBase的映射
	showMap := make(map[uint64]*showDto.ShowBase)
	for _, show := range shows {
		showMap[show.ID] = show
	}

	// 按原始排序返回结果
	orderedShows := make([]*showDto.ShowBase, 0, len(orderedIDs))
	for _, id := range orderedIDs {
		if show, exists := showMap[id]; exists {
			// 可以在这里添加相关度分数到结果中（如果需要的话）
			orderedShows = append(orderedShows, show)
		}
	}

	return orderedShows
}

// 辅助方法

// mapFieldIDsToCategories 将FieldID映射到分类名称
func (e *Entry) mapFieldIDsToCategories(fieldIDs []uint64) []string {
	// 这里需要根据实际的数据库设计来实现
	// 现在返回空，表示不进行分类筛选
	return []string{}
}

// mapGenreIDToName 将GenreID映射到类型名称
func (e *Entry) mapGenreIDToName(genreID uint64) string {
	// 这里需要根据实际的数据库设计来实现
	// 现在返回空字符串
	return ""
}

// mapSortType 将排序类型映射到字符串
func (e *Entry) mapSortType(sortType uint32) string {
	switch sortType {
	case 1:
		return "year" // 最新
	case 2:
		return "created_at" // 最热
	case 3:
		return "rating" // 评分
	default:
		return "relevance"
	}
}

// IsVectorSearchEnabled 检查是否应该启用向量搜索
func (e *Entry) IsVectorSearchEnabled(req *showDto.ShowSearchReq) bool {
	// 检查是否有关键词
	if req.Keyword == "" {
		return false
	}

	// 检查是否明确启用向量搜索
	if req.UseVector {
		return true
	}

	// 默认策略：关键词长度大于2个字符时启用向量搜索
	return len(strings.TrimSpace(req.Keyword)) > 2
}

// GetSearchSuggestions 获取搜索建议（可选功能）
func (e *Entry) GetSearchSuggestions(ctx *gin.Context, query string, language string, limit int) ([]string, error) {
	// 使用向量搜索服务获取建议
	suggestionReq := &dto.SearchSuggestionRequest{
		Query:    query,
		Language: language,
		Limit:    limit,
	}

	searchService := search.NewSearchService()
	suggestions, err := searchService.GetSearchSuggestions(ctx, suggestionReq)
	if err != nil {
		return []string{}, nil // 失败时返回空建议
	}

	// 提取建议文本
	result := make([]string, 0, len(suggestions.Suggestions))
	for _, suggestion := range suggestions.Suggestions {
		result = append(result, suggestion.Text)
	}

	return result, nil
}
