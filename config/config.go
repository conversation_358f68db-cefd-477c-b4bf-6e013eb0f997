package config

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"errors"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"gopkg.in/ini.v1"
)

type App struct {
	AppName      string
	Env          string
	JwtSecret    string
	JwtTimeout   int
	HttpPort     int
	LogPath      string
	LogLevel     string
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
}

var AppCfg = &App{}

type Database struct {
	Host     string
	Port     int
	Database string
	Username string
	Password string
	Loc      string // TablePrefix string
}

var DbCfg = &Database{}
var DbReadCfg = &Database{}

type Redis struct {
	Host        string
	Username    string
	Password    string
	Prefix      string
	Db          int
	MaxIdle     int
	MaxActive   int
	IdleTimeout time.Duration
}

var RedisCfg = &Redis{}

var TmdbCfg = &Tmdb{}

type Tmdb struct {
	Url string
}

var AliyunCfg = &Aliyun{}

type Aliyun struct {
	RegionID        string
	OssRegionID     string
	VodRegionID     string
	AccessKeyID     string
	AccessKeySecret string
	RoleArn         string
	RamUID          string
	BucketImage     string
	BucketVideo     string
	BucketVideoMps  string
	MpsPipelineId   string
}

var BytePlusCfg = &BytePlus{}

type BytePlus struct {
	Endpoint        string
	OssBaseHost     string
	OssImageHost    string
	OssSubtitleHost string
	RegionID        string
	OssRegionID     string
	VodRegionID     string
	AccessKeyID     string
	AccessKeySecret string
	RoleTrn         string
	IamUID          string

	BucketImage    string
	BucketSubtitle string
	BucketVideo    string
	BucketVideoMps string

	Ak        string
	Sk        string
	SpaceName string
}

var PayPalCfg = &PayPal{}

type PayPal struct {
	ClientID  string
	Secret    string
	WebHookID string
	// Sandbox      bool
	// ReturnURL    string
	// CancelURL    string
	// NotifyURL    string
	// CurrencyCode string
}

var (
	SkyEmailCfg   = &Email{}
	ViEmailCfg    = &Email{}
	HitvEmailCfg  = &Email{}
	AhatvEmailCfg = &Email{}
)

var VikingDBCfg = &VikingDB{}

type VikingDB struct {
	NeedEmbed         bool
	Host              string
	Region            string
	AccessKeyID       string
	AccessKeySecret   string
	Scheme            string
	ConnectionTimeout int
	Collection        string
	Index             string
	ModelName         string
	UseSparse         bool
	DenseWeight       float64
	BatchSize         int
	MaxRetries        int
	RetryDelay        int
	// 新增：文档模式配置
	DocumentMode      string  // "merged" | "split"
	TextMergeStrategy string  // "simple" | "structured" | "weighted"
	TitleWeight       float64 // 标题权重倍数
	// 新增：异步上传配置
	UseAsyncUpload bool // 是否使用异步上传模式
	// 新增：向量维度配置
	VectorDimension int // 向量维度，默认 1024
	// 新增：上传超时配置
	UploadTimeout int // 上传操作超时时间（秒）
}

type Email struct {
	Host     string
	Port     int
	Username string
	Password string
	From     string
	FromName string
}

var cfgPath = "config/test.ini"

func Setup() {
	SetupWithPath("")
}

func SetupWithPath(customPath string) {
	// 确定配置文件路径的优先级：
	// 1. 自定义路径参数
	// 2. 环境变量
	// 3. 默认路径
	if customPath != "" {
		cfgPath = customPath
	} else if cfg := os.Getenv("config"); cfg != "" {
		cfgPath = cfg
	}

	log.Printf("config path:%s\n", cfgPath)

	// 检查配置文件是否存在
	if _, err := os.Stat(cfgPath); os.IsNotExist(err) {
		log.Fatalf("config file not found: %s\n", cfgPath)
	}

	cfg, err := ini.Load(cfgPath)
	if err != nil {
		log.Fatalf("config load error:%v\n", err)
	}
	err = ini.MapTo(AppCfg, cfgPath)
	if err != nil {
		log.Fatalf("config map app error:%v\n", err)
	}

	mapConfig(cfg, "app", AppCfg)
	mapConfig(cfg, "mysql", DbCfg)
	mapConfig(cfg, "mysql_read", DbReadCfg)
	mapConfig(cfg, "redis", RedisCfg)
	mapConfig(cfg, "aliyun", AliyunCfg)
	mapConfig(cfg, "tmdb", TmdbCfg)
	mapConfig(cfg, "byteplus", BytePlusCfg)
	mapConfig(cfg, "paypal", PayPalCfg)
	mapConfig(cfg, "email.skybox", SkyEmailCfg)
	mapConfig(cfg, "email.vibox", ViEmailCfg)
	mapConfig(cfg, "email.hitv", HitvEmailCfg)
	mapConfig(cfg, "email.ahatv", AhatvEmailCfg)
	mapConfig(cfg, "vikingdb", VikingDBCfg)

	// 设置VikingDB的默认值
	if VikingDBCfg.ConnectionTimeout <= 0 {
		VikingDBCfg.ConnectionTimeout = 30 // 默认30秒连接超时
	}
	if VikingDBCfg.UploadTimeout <= 0 {
		VikingDBCfg.UploadTimeout = 60 // 默认60秒上传超时
	}
	if VikingDBCfg.BatchSize <= 0 {
		VikingDBCfg.BatchSize = 100 // 默认批量大小
	}
	if VikingDBCfg.MaxRetries <= 0 {
		VikingDBCfg.MaxRetries = 3 // 默认重试次数
	}
	if VikingDBCfg.RetryDelay <= 0 {
		VikingDBCfg.RetryDelay = 1000 // 默认重试延迟1秒
	}

	// 验证VikingDB配置
	validateVikingDBConfig()

	AppCfg.ReadTimeout = AppCfg.ReadTimeout * time.Second
	AppCfg.WriteTimeout = AppCfg.WriteTimeout * time.Second
	RedisCfg.IdleTimeout = RedisCfg.IdleTimeout * time.Second
}

// validateVikingDBConfig 验证VikingDB配置
func validateVikingDBConfig() {
	if VikingDBCfg == nil {
		log.Println("Warning: VikingDB configuration is nil")
		return
	}

	// 检查必要的配置项
	if VikingDBCfg.Host == "" || strings.Contains(VikingDBCfg.Host, "your-") {
		log.Printf("Warning: VikingDB Host is not properly configured: %s\n", VikingDBCfg.Host)
	}

	if VikingDBCfg.AccessKeyID == "" || strings.Contains(VikingDBCfg.AccessKeyID, "your-") {
		log.Println("Warning: VikingDB AccessKeyID is not properly configured")
	}

	if VikingDBCfg.AccessKeySecret == "" || strings.Contains(VikingDBCfg.AccessKeySecret, "your-") {
		log.Println("Warning: VikingDB AccessKeySecret is not properly configured")
	}

	if VikingDBCfg.Collection == "" {
		log.Println("Warning: VikingDB Collection is not configured")
	}

	if VikingDBCfg.Region == "" {
		log.Println("Warning: VikingDB Region is not configured")
	}

	// 输出当前配置（隐藏敏感信息）
	log.Printf("VikingDB Config: Host=%s, Region=%s, Collection=%s, Index=%s\n",
		VikingDBCfg.Host,
		VikingDBCfg.Region,
		VikingDBCfg.Collection,
		VikingDBCfg.Index,
	)
}

func mapConfig(cfg *ini.File, section string, v interface{}) {
	err := cfg.Section(section).MapTo(v)
	if err != nil {
		log.Fatalf("config map %s error:%v\n", section, err)
	}
}

func LoadPrivateKeyByFilename(fileName string) ([]byte, error) {
	keyData, err := os.ReadFile(fmt.Sprintf("config/%s", fileName))
	if err != nil {
		return nil, fmt.Errorf("read private key err: %v", err)
	}

	return keyData, nil
}

func LoadWxPrivateKey() ([]byte, error) {
	keyData, err := os.ReadFile("config/apiclient_key.pem")
	if err != nil {
		return nil, fmt.Errorf("read private key err: %v", err)
	}

	// 验证PEM格式（可选但建议）
	block, _ := pem.Decode(keyData)
	if block == nil || block.Type != "PRIVATE KEY" {
		return nil, errors.New("private key invalid")
	}

	return keyData, nil
}

func LoadWxPrivateKeyRsa() (privateKey *rsa.PrivateKey, err error) {
	data, err := os.ReadFile("./apiclient_key.pem")
	if err != nil {
		return nil, fmt.Errorf("read private key err: %v", err)
	}

	return LoadPrivateKey(data)
}

func LoadPrivateKey(byteKey []byte) (privateKey *rsa.PrivateKey, err error) {
	block, _ := pem.Decode(byteKey)
	if block == nil {
		return nil, fmt.Errorf("decode private key err")
	}
	if block.Type != "PRIVATE KEY" {
		return nil, fmt.Errorf("the kind of PEM should be PRVATE KEY")
	}
	key, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("parse private key err:%s", err.Error())
	}
	privateKey, ok := key.(*rsa.PrivateKey)
	if !ok {
		return nil, fmt.Errorf("not a RSA private key")
	}
	return privateKey, nil
}
