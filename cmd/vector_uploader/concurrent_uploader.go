package main

import (
	"fmt"
	"log"
	"sync"
	"time"

	"vlab/config"

	"github.com/volcengine/volc-sdk-golang/service/vikingdb"
)

// ConcurrentUploader 并发上传器
type ConcurrentUploader struct {
	config        *config.VikingDB
	queue         chan vikingdb.Data
	stopChan      chan struct{}
	wg            sync.WaitGroup
	progressBar   *ProgressBar
	totalItems    int64
	uploadedItems int64
	mu            sync.Mutex
}

// NewConcurrentUploader 创建并发上传器
func NewConcurrentUploader(cfg *config.VikingDB, queueSize int) *ConcurrentUploader {
	return &ConcurrentUploader{
		config:   cfg,
		queue:    make(chan vikingdb.Data, queueSize),
		stopChan: make(chan struct{}),
	}
}

// consumer 消费者goroutine
func (cu *ConcurrentUploader) consumer(workerID int) {
	defer cu.wg.Done()

	// 创建VikingDB Service并设置超时
	vikingDBService := vikingdb.NewVikingDBService(
		cu.config.Host,
		cu.config.Region,
		cu.config.AccessKeyID,
		cu.config.AccessKeySecret,
		cu.config.Scheme,
	)

	// 设置连接超时
	if cu.config.ConnectionTimeout > 0 {
		vikingDBService.SetConnectionTimeout(int64(cu.config.ConnectionTimeout))
		log.Printf("Worker %d: 设置VikingDB连接超时: %d秒", workerID, cu.config.ConnectionTimeout)
	}

	// 创建VikingDB客户端
	collectionClient := vikingdb.NewCollectionClient(
		cu.config.Collection,
		cu.config.Host,
		cu.config.Region,
		cu.config.AccessKeyID,
		cu.config.AccessKeySecret,
		cu.config.Scheme,
	)

	items := make([]vikingdb.Data, 0, cu.config.BatchSize)

	log.Printf("Worker %d 启动", workerID)

	for {
		select {
		case item := <-cu.queue:
			items = append(items, item)

			// 当达到批次大小时上传
			if len(items) >= cu.config.BatchSize {
				cu.uploadBatchWithRetry(collectionClient, items, workerID)
				cu.updateProgress(int64(len(items)))
				items = items[:0] // 清空切片
			}

		case <-cu.stopChan:
			// 上传剩余的数据
			if len(items) > 0 {
				cu.uploadBatchWithRetry(collectionClient, items, workerID)
				cu.updateProgress(int64(len(items)))
			}
			log.Printf("Worker %d 停止", workerID)
			return
		}
	}
}

// uploadBatchWithRetry 带重试的批量上传
func (cu *ConcurrentUploader) uploadBatchWithRetry(client *vikingdb.CollectionClient, items []vikingdb.Data, workerID int) {
	var lastErr error

	for i := 0; i < cu.config.MaxRetries; i++ {
		// 使用异步上传模式提高并发性能
		var err error
		if cu.config.UseAsyncUpload {
			err = client.UpsertData(items, vikingdb.WithAsyncUpsert(true))
		} else {
			err = client.UpsertData(items)
		}

		if err == nil {
			log.Printf("Worker %d 成功上传 %d 个文档", workerID, len(items))
			return
		}

		lastErr = err
		log.Printf("Worker %d 上传失败，第 %d 次重试: %v", workerID, i+1, err)

		if i < cu.config.MaxRetries-1 {
			time.Sleep(time.Duration(cu.config.RetryDelay) * time.Millisecond)
		}
	}

	log.Printf("Worker %d 重试 %d 次后仍然失败: %v", workerID, cu.config.MaxRetries, lastErr)
}

// updateProgress 更新进度
func (cu *ConcurrentUploader) updateProgress(uploaded int64) {
	cu.mu.Lock()
	defer cu.mu.Unlock()

	cu.uploadedItems += uploaded

	if cu.progressBar != nil {
		cu.progressBar.Update(cu.uploadedItems, fmt.Sprintf("已上传 %d 个文档", cu.uploadedItems))
	}
}

// StartWorkers 启动工作线程
func (cu *ConcurrentUploader) StartWorkers(numWorkers int) {
	log.Printf("启动 %d 个工作线程", numWorkers)

	for i := 0; i < numWorkers; i++ {
		cu.wg.Add(1)
		go cu.consumer(i + 1)
	}
}

// AddData 添加数据到队列
func (cu *ConcurrentUploader) AddData(data vikingdb.Data) {
	cu.queue <- data
}

// AddDataBatch 批量添加数据到队列
func (cu *ConcurrentUploader) AddDataBatch(dataList []vikingdb.Data) {
	for _, data := range dataList {
		cu.queue <- data
	}
}

// SetProgressBar 设置进度条
func (cu *ConcurrentUploader) SetProgressBar(pb *ProgressBar) {
	cu.progressBar = pb
}

// SetTotalItems 设置总数据量
func (cu *ConcurrentUploader) SetTotalItems(total int64) {
	cu.totalItems = total
}

// Stop 停止上传器
func (cu *ConcurrentUploader) Stop() {
	close(cu.stopChan)
	cu.wg.Wait()
	log.Println("所有工作线程已停止")
}
