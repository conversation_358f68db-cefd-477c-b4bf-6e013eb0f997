package show

import (
	"sort"
	"strings"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"vlab/app/api/tmdb"
	"vlab/app/common/dbs"
	"vlab/app/dao"
	classFieldDao "vlab/app/dao/content_class_field"
	creditDao "vlab/app/dao/content_credit"
	episodeDao "vlab/app/dao/content_episode"
	franchiseDao "vlab/app/dao/content_franchise"
	genreDao "vlab/app/dao/content_genre"
	i18nDao "vlab/app/dao/content_i18n"
	imageDao "vlab/app/dao/content_image"
	personDao "vlab/app/dao/content_person"
	posterDao "vlab/app/dao/content_poster"
	showDao "vlab/app/dao/content_show"
	channelDao "vlab/app/dao/resource_channel"
	channelkey "vlab/app/dao/resource_channel_key"
	versionDao "vlab/app/dao/resource_version"
	resourceDto "vlab/app/dto/resource"
	showDto "vlab/app/dto/show"
	"vlab/app/service/resource"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
)

// reorderAdminShowListByIDs 根据向量搜索返回的ID顺序重新排序AdminShow列表
func reorderAdminShowListByIDs(showList []*showDto.AdminShow, orderedIDs []uint64) []*showDto.AdminShow {
	// 创建ID到索引的映射
	idToIndex := make(map[uint64]int, len(orderedIDs))
	for idx, id := range orderedIDs {
		idToIndex[id] = idx
	}

	// 创建ID到AdminShow的映射
	idToShow := make(map[uint64]*showDto.AdminShow, len(showList))
	for _, show := range showList {
		idToShow[show.ID] = show
	}

	// 按照orderedIDs的顺序重新组装结果
	result := make([]*showDto.AdminShow, 0, len(showList))
	for _, id := range orderedIDs {
		if show, exists := idToShow[id]; exists {
			result = append(result, show)
		}
	}

	return result
}

func (e Entry) AdminShowList(ctx *gin.Context, req *showDto.AdminShowListReq) (resp *showDto.AdminShowListResp, err error) {
	resp = &showDto.AdminShowListResp{}
	resp.List = make([]*showDto.AdminShow, 0)

	// 判断是否使用向量搜索
	var useVectorSearch bool
	var vectorShowIDs []uint64
	var vectorRequestID string

	if req.Name != "" && shouldUseVectorSearch(ctx, req) {
		useVectorSearch = true
		req.VectorTopK = req.Limit
		// 设置向量搜索的默认参数
		setVectorSearchDefaults(req)

		// 使用增强搜索服务执行向量搜索
		enhancedService := NewEnhancedAdminSearchService(&e)
		vectorShowIDs, vectorRequestID, err = enhancedService.EnhancedAdminShowSearch(ctx, req)
		if err != nil || len(vectorShowIDs) == 0 {
			// 向量搜索失败或无结果，回退到传统搜索
			useVectorSearch = false
		}
		resp.RequestID = vectorRequestID
	}

	filter := &showDao.Filter{
		FranchiseID: req.FranchiseID,
		ContentType: req.ContentType,
		Status:      req.Status,
		ChannelID:   req.ChannelID,
		VersionID:   req.VersionID,
	}

	// 根据搜索模式设置过滤条件
	if useVectorSearch && len(vectorShowIDs) > 0 {
		// 使用向量搜索结果
		filter.IDs = vectorShowIDs
	} else if req.Name != "" {
		// 使用传统的名称模糊搜索
		filter.NameLike = req.Name
	}

	if req.GenreID > 0 {
		filter.GenreIDs = append(filter.GenreIDs, req.GenreID)
	}

	cnt, list, err := e.ShowRepo.DataPageList(ctx, filter,
		req.Page, req.Limit)
	if err != nil {
		return nil, err
	}
	if cnt == 0 {
		return
	}
	resp.Count = cnt
	if len(list) == 0 {
		return resp, nil
	}

	var (
		i18nKeyList = make([]string, 0)
		i18nKeys    = make(i18nDao.I18nList, 0)
		i18nKeyMap  = make(map[string][]*i18nDao.I18n)
		posters     = make(posterDao.PosterList, 0)

		imageIDs = make([]uint64, 0)
		images   = make([]*imageDao.Image, 0)
		showIDs  = list.GetIDs()

		franchiseIDs = make([]uint64, 0)
		franchises   = make(franchiseDao.FranchiseList, 0)
		withGenres   = make(showDao.ShowWithGenreList, 0)
		genreIDs     = make([]uint64, 0)
		genres       = make(genreDao.GenreList, 0)

		withLimits = make(showDao.ShowWithLimitList, 0)
		channelIDs = make([]uint64, 0)
		versionIDs = make([]uint64, 0)
		channelMap = make(map[uint64]*channelDao.Model)
		versionMap = make(map[uint64]*versionDao.Model)
	)

	channelKeyInfo, _ := helper.GetCtxChannelKeyInfo(ctx, true)
	if !channelKeyInfo.CheckOssImageHost() {
		return nil, ecode.ChannelConfigInvalidErr
	}

	for _, showModel := range list {
		i18nKeyList = append(i18nKeyList,
			showModel.NameKey, showModel.OverviewKey, showModel.AirDateKey)

		franchiseIDs = append(franchiseIDs, showModel.FranchiseID)
	}

	{

		franchises, err = e.FranchiseRepo.FindByFilter(ctx, &franchiseDao.Filter{
			IDs: franchiseIDs,
		})
		i18nKeyList = append(i18nKeyList, franchises.GetNameKeys()...)

		// genres
		withGenres, err = e.ShowRepo.FindGenreByFilter(ctx, &showDao.GenreFilter{
			ShowIDs: showIDs,
		})
		genreIDs = withGenres.GetGenreIDs()

		genres, err = e.GenreRepo.FindByFilter(ctx, &genreDao.Filter{
			IDs: genreIDs,
		})
		i18nKeyList = append(i18nKeyList, genres.GetNameKeys()...)
	}

	{
		withLimits, err = e.ShowRepo.FindLimitByFilter(ctx, &showDao.LimitFilter{
			ShowIDs: showIDs,
			Type:    uint32(showDao.LimitTypeChannel),
		})
		channelIDs = withLimits.GetChannelIDs()
		versionIDs = withLimits.GetVersionIDs()

		channels, err := resource.GetService().GetChannels(ctx, channelIDs)
		if err != nil {
			return nil, err
		}
		channelMap = channels.GetIDMap()

		versions, err := resource.GetService().GetVersions(ctx, versionIDs)
		if err != nil {
			return nil, err
		}
		versionMap = versions.GetIDMap()

	}

	posters, err = e.PosterRepo.FindByFilter(ctx, &posterDao.Filter{
		ShowIDs: showIDs,
	})
	imageIDs = append(imageIDs, posters.GetImageIDs()...)

	images, err = e.ImageRepo.FindByFilter(ctx, &imageDao.Filter{
		IDs: imageIDs,
	})

	// 校验ISO_639_1
	i18nKeys, err = e.I18nRepo.FindByFilter(ctx, &i18nDao.Filter{
		Keys:      i18nKeyList,
		ISO_639_1: req.ISO_639_1,
	})

	i18nKeyMap = i18nKeys.GetI18nKey()

	e.setAdminShowListResp(
		req,
		resp,
		list,
		posters,
		images,
		genres,
		withGenres,
		franchises,
		i18nKeyMap,
		channelKeyInfo,
	)

	showEpisodeMap, err := e.getAdminShowEpisodeInfo(ctx, showIDs)
	if err != nil {
		return resp, nil
	}
	if len(showEpisodeMap) > 0 {
		for _, item := range resp.List {
			if episodeCount, ok := showEpisodeMap[item.ID]; ok {
				item.NumberOfEpisodes = uint32(episodeCount)
			}
		}
	}
	if len(channelMap) > 0 {
		showLimitsMap := withLimits.GetShowIDMap()
		for _, item := range resp.List {
			ls, ok := showLimitsMap[item.ID]
			if !ok {
				continue
			}
			for _, l := range ls {
				channel, ok := channelMap[l.LimitID]
				if !ok {
					continue
				}
				item.Channels = append(item.Channels, &resourceDto.AdminChannelListItem{
					ID:   channel.ID,
					Name: channel.Name,
				})
			}
		}
	}
	if len(versionMap) > 0 {
		showLimitsMap := withLimits.GetShowIDMap()
		for _, item := range resp.List {
			ls, ok := showLimitsMap[item.ID]
			if !ok {
				continue
			}
			for _, l := range ls {
				version, ok := versionMap[l.LimitID]
				if !ok {
					continue
				}
				item.Versions = append(item.Versions, &resourceDto.AdminVersionListItem{
					ID:      version.ID,
					Name:    version.Name,
					Version: version.Version,
				})
			}
		}
	}

	// 如果使用了向量搜索，需要按照向量搜索返回的ID顺序重新排序结果
	if useVectorSearch && len(vectorShowIDs) > 0 {
		resp.List = reorderAdminShowListByIDs(resp.List, vectorShowIDs)
	}

	return
}

func (e Entry) setAdminShowListResp(
	req *showDto.AdminShowListReq,
	resp *showDto.AdminShowListResp,
	showList showDao.ShowList,
	posters posterDao.PosterList,
	images imageDao.ImageList,
	genres genreDao.GenreList,
	withGenres showDao.ShowWithGenreList,
	franchises franchiseDao.FranchiseList,
	i18nKeyMap map[string][]*i18nDao.I18n,
	channelKeyInfo *channelkey.Model,
) {
	var (
		imageMap      = images.GetMap()
		posterShowMap = posters.GetShowMap()
		showGenreMap  = withGenres.GetShowIDMap()
		genreMap      = genres.GetMap()
		franchiseMap  = franchises.GetMap()
	)
	for _, showModel := range showList {
		item := &showDto.AdminShow{
			ShowBase:          &showDto.ShowBase{},
			NameKey:           showModel.NameKey,
			OverviewKey:       showModel.OverviewKey,
			AirDateKey:        showModel.AirDateKey,
			GenresKey:         make([]string, 0),
			FranchiseKey:      "",
			ShowNameI18n:      make([]*dao.I18n, 0),
			ShowOverViewI18n:  make([]*dao.I18n, 0),
			ShowAirDateI18n:   make([]*dao.I18n, 0),
			ShowGenresI18n:    make([]*dao.I18n, 0),
			ShowFranchiseI18n: make([]*dao.I18n, 0),
		}
		item.ID = showModel.ID
		item.Status = showModel.Status

		// Name 随语言
		item.NameKey = showModel.NameKey
		if names, ok := i18nKeyMap[showModel.NameKey]; ok {
			for _, name := range names {
				if name.ISO_639_1 == req.ISO_639_1 {
					item.Name = name.Value
				}
				item.ShowNameI18n = append(item.ShowNameI18n, &dao.I18n{
					ISO_639_1: name.ISO_639_1,
					Value:     name.Value,
				})
			}
		} else {
			item.Name = showModel.Name
		}

		item.OriginalName = showModel.Name
		// Overview 随语言
		item.OverviewKey = showModel.OverviewKey
		if overviews, ok := i18nKeyMap[showModel.NameKey]; ok {
			for _, overview := range overviews {
				if overview.ISO_639_1 == req.ISO_639_1 {
					item.Overview = overview.Value
				}
				item.ShowOverViewI18n = append(item.ShowOverViewI18n, &dao.I18n{
					ISO_639_1: overview.ISO_639_1,
					Value:     overview.Value,
				})
			}
		} else {
			item.Overview = showModel.Overview
		}

		item.Score = showModel.Score
		item.AirDate = showModel.AirDate
		item.AirDateKey = showModel.AirDateKey
		if airDates, ok := i18nKeyMap[showModel.AirDateKey]; ok {
			for _, airDate := range airDates {
				if airDate.ISO_639_1 == req.ISO_639_1 {
					item.AirDate = airDate.Value
				}
				item.ShowAirDateI18n = append(item.ShowAirDateI18n, &dao.I18n{
					ISO_639_1: airDate.ISO_639_1,
					Value:     airDate.Value,
				})
			}
		} else {
			item.AirDate = showModel.AirDate
		}

		item.ContentType = uint32(showModel.ContentType)
		item.InProduction = showModel.InProduction.Uint32()

		// genres
		if genres, ok := showGenreMap[showModel.ID]; ok {
			for _, genre := range genres {
				genreBase := &showDto.GenreBase{
					Id: genre.ID,
				}
				if genre, ok := genreMap[genre.GenreID]; ok {
					if names, ok := i18nKeyMap[genre.NameKey]; ok {
						for _, name := range names {
							if name.ISO_639_1 == req.ISO_639_1 {
								genreBase.Name = name.Value
							}
							genreBase.NameKey = name.Key
							genreBase.NameI18n = append(genreBase.NameI18n, &dao.I18n{
								ISO_639_1: name.ISO_639_1,
								Value:     name.Value,
							})
						}
					} else {
						genreBase.Name = genre.Name
					}
				}
				if genreBase.NameKey != "" {
					item.GenresKey = append(item.GenresKey, genreBase.NameKey)
				}
				item.Genres = append(item.Genres, genreBase)
			}
		}

		// franchise
		if franchise, ok := franchiseMap[showModel.FranchiseID]; ok {
			item.Franchise = &showDto.FranchiseBase{
				Id: franchise.ID,
			}
			item.Franchise.NameKey = franchise.NameKey
			if names, ok := i18nKeyMap[franchise.NameKey]; ok {
				for _, name := range names {
					if name.ISO_639_1 == req.ISO_639_1 {
						item.Franchise.Name = name.Value
					}
				}
			}
		}

		if posters, ok := posterShowMap[showModel.ID]; ok {
			for _, poster := range posters {
				if image, ok := imageMap[poster.ImageID]; ok {
					item.Posters = append(item.Posters, &showDto.ImageBase{
						AspectRatio: image.AspectRatio,
						Height:      image.Height,
						Iso6391:     image.ISO_639_1,
						FilePath:    image.GetFilePath(channelKeyInfo.OssImageHost),
						Width:       image.Width,
						AspectType:  image.SetAspectType(),
					})
				}
			}
		}
		resp.List = append(resp.List, item)
	}
	return
}

func (e Entry) setAdminShowDetailResp(
	req *showDto.AdminShowDetailReq,
	resp *showDto.AdminShowDetailResp,
	showModel *showDao.Show,
	credits []*creditDao.Credit,
	episodes episodeDao.EpisodeList,
	posters []*posterDao.Poster,
	persons personDao.PersonList,
	images imageDao.ImageList,
	genres genreDao.GenreList,
	withGenres showDao.ShowWithGenreList,
	franchises franchiseDao.FranchiseList,
	i18nKeyMap map[string][]*i18nDao.I18n,
	channelKeyInfo *channelkey.Model,
) {

	var (
		imageMap       = images.GetMap()
		personMap      = persons.GetMap()
		episodeShowMap = episodes.GetShowKeyMap()

		genreMap     = genres.GetMap()
		showGenreMap = withGenres.GetShowIDMap()
		franchiseMap = franchises.GetMap()
	)

	resp.ShowBase = &showDto.ShowBase{}
	resp.Franchise = &showDto.FranchiseBase{}
	resp.Credits = &showDto.Credits{}
	resp.Images = &showDto.Images{}
	resp.Images.Posters = make([]*showDto.ImageBase, 0)
	resp.Images.BackDrops = make([]*showDto.ImageBase, 0)
	resp.Credits.Casts = make([]*showDto.CreditBase, 0)
	resp.Credits.Crews = make([]*showDto.CreditBase, 0)
	resp.ShowBase.Genres = make([]*showDto.GenreBase, 0)
	resp.ShowBase.Posters = make([]*showDto.ImageBase, 0)
	resp.CreatedBy = make([]*showDto.CreditBase, 0)
	resp.Episodes = make([]*showDto.EpisodeBase, 0)
	//resp.Seasons = make([]*showDto.SeasonBase, 0)

	resp.ID = showModel.ID
	resp.Status = showModel.Status

	// Name 随语言
	resp.NameKey = showModel.NameKey
	if names, ok := i18nKeyMap[showModel.NameKey]; ok {
		for _, name := range names {
			if name.ISO_639_1 == req.ISO_639_1 {
				resp.Name = name.Value
			}
			resp.ShowNameI18n = append(resp.ShowNameI18n, &dao.I18n{
				ISO_639_1: name.ISO_639_1,
				Value:     name.Value,
			})
		}
	} else {
		resp.Name = showModel.Name
	}
	resp.OriginalName = showModel.Name
	// Overview 随语言
	resp.OverviewKey = showModel.OverviewKey
	if overviews, ok := i18nKeyMap[showModel.OverviewKey]; ok && len(overviews) > 0 {

		for _, overview := range overviews {
			if req.ISO_639_1 != "" && overview.ISO_639_1 == req.ISO_639_1 {
				resp.Overview = overview.Value
				resp.OverviewKey = overview.Key
				break
			}
		}
		if resp.Overview == "" {
			resp.Overview = overviews[0].Value
		}
	} else {
		resp.Overview = showModel.Overview
	}

	resp.AirDate = showModel.AirDate
	resp.AirDateKey = showModel.AirDateKey
	if airDates, ok := i18nKeyMap[showModel.AirDateKey]; ok && len(airDates) > 0 {

		for _, airDate := range airDates {
			if req.ISO_639_1 != "" && airDate.ISO_639_1 == req.ISO_639_1 {
				resp.AirDate = airDate.Value
				break
			}
		}
		if resp.AirDate == "" {
			resp.AirDate = airDates[0].Value
		}
	} else {
		resp.AirDate = showModel.AirDate
	}

	resp.Score = showModel.Score
	resp.ContentType = uint32(showModel.ContentType)
	resp.InProduction = showModel.InProduction.Uint32()

	resp.Homepage = showModel.Homepage

	// genres
	if genres, ok := showGenreMap[showModel.ID]; ok {
		for _, withGenre := range genres {
			genreBase := &showDto.GenreBase{}
			if genre, ok := genreMap[withGenre.GenreID]; ok {
				genreBase.Id = withGenre.GenreID
				if names, ok := i18nKeyMap[genre.NameKey]; ok {
					for _, name := range names {
						if name.ISO_639_1 == req.ISO_639_1 {
							genreBase.Name = name.Value
						}
						genreBase.NameKey = name.Key
						genreBase.NameI18n = append(genreBase.NameI18n, &dao.I18n{
							ISO_639_1: name.ISO_639_1,
							Value:     name.Value,
						})
					}
				} else {
					genreBase.Name = genre.Name
				}
			}
			if genreBase.NameKey != "" {
				resp.GenresKey = append(resp.GenresKey, genreBase.NameKey)
			}
			resp.Genres = append(resp.Genres, genreBase)
		}
	}

	// franchise
	if franchise, ok := franchiseMap[showModel.FranchiseID]; ok {
		resp.Franchise = &showDto.FranchiseBase{
			Id: franchise.ID,
		}
		resp.Franchise.NameKey = franchise.NameKey
		if names, ok := i18nKeyMap[franchise.NameKey]; ok {
			for _, name := range names {
				if name.ISO_639_1 == req.ISO_639_1 {
					resp.Franchise.Name = name.Value
				}
			}
		}
	}

	// credits
	for _, credit := range credits {
		creditBase := &showDto.CreditBase{
			Id:         credit.ID,
			Job:        uint32(credit.Job),
			Department: credit.Department,
			Character:  credit.Character,
			Order:      credit.Order,
		}
		creditBase.PersonBase = &showDto.PersonBase{}
		if person, ok := personMap[credit.PersonID]; ok {

			if name, ok := i18nKeyMap[person.NameKey]; ok && len(name) > 0 {
				creditBase.Name = name[0].Value
			}
			creditBase.OriginalName = person.Name
			creditBase.PersonID = person.ID
			creditBase.Gender = person.Gender
			if image, ok := imageMap[person.ProfileID]; ok {
				creditBase.ProfilePath = image.GetFilePath(channelKeyInfo.OssImageHost)
			}
		}

		if credit.Job == creditDao.CreditJobDirector {
			resp.CreatedBy = append(resp.CreatedBy, creditBase)
		}
		if credit.Job == creditDao.CreditJobActor {
			resp.Credits.Casts = append(resp.Credits.Casts, creditBase)
		} else {
			resp.Credits.Crews = append(resp.Credits.Crews, creditBase)
		}
	}

	sort.Slice(resp.CreatedBy, func(i, j int) bool {
		return resp.CreatedBy[i].Order < resp.CreatedBy[j].Order
	})
	// sort credits by order
	sort.Slice(resp.Credits.Casts, func(i, j int) bool {
		return resp.Credits.Casts[i].Order < resp.Credits.Casts[j].Order
	})
	sort.Slice(resp.Credits.Crews, func(i, j int) bool {
		return resp.Credits.Crews[i].Order < resp.Credits.Crews[j].Order
	})

	// seasons

	// episodes
	if episodes, ok := episodeShowMap[showModel.ID]; ok {
		//seasonBase.EpisodeCount = uint32(len(episodes))
		for _, episode := range episodes {
			episodeBase := &showDto.EpisodeBase{
				Id:            episode.ID,
				Name:          episode.Name,
				EpisodeNumber: episode.EpisodeNumber,
			}
			resp.Episodes = append(resp.Episodes, episodeBase)
		}
	}

	// posters
	for _, poster := range posters {
		if image, ok := imageMap[poster.ImageID]; ok {
			imageBase := &showDto.ImageBase{
				AspectRatio: image.AspectRatio,
				Height:      image.Height,
				Iso6391:     image.ISO_639_1,
				FilePath:    image.GetFilePath(channelKeyInfo.OssImageHost),
				Width:       image.Width,
				AspectType:  image.SetAspectType(),
			}
			resp.Images.Posters = append(resp.Images.Posters, imageBase)

			resp.Posters = append(resp.Posters, imageBase)
		}

	}
	return
}

func (e Entry) AdminShowDetail(ctx *gin.Context, req *showDto.AdminShowDetailReq) (resp *showDto.AdminShowDetailResp, err error) {
	resp = &showDto.AdminShowDetailResp{}
	resp.AdminShow = showDto.AdminShow{}
	resp.ShowBase = &showDto.ShowBase{}

	channelKeyInfo, _ := helper.GetCtxChannelKeyInfo(ctx, true)
	if !channelKeyInfo.CheckOssImageHost() {
		return nil, ecode.ChannelConfigInvalidErr
	}

	var (
		showModel = &showDao.Show{}
		credits   = make(creditDao.CreditList, 0)
		//seasons     = []*seasonDao.Season{}
		posters     = make(posterDao.PosterList, 0)
		i18nKeyList = make([]string, 0)
		i18nKeys    = make(i18nDao.I18nList, 0)
		personIDs   = make([]uint64, 0)
		persons     = make(personDao.PersonList, 0)
		episodes    = make([]*episodeDao.Episode, 0)

		imageIDs = make([]uint64, 0)
		images   = make([]*imageDao.Image, 0)

		franchises = make(franchiseDao.FranchiseList, 0)
		withGenres = make(showDao.ShowWithGenreList, 0)
		genreIDs   = make([]uint64, 0)
		genres     = make(genreDao.GenreList, 0)

		showID = req.ID

		withLimits = make(showDao.ShowWithLimitList, 0)
		channelIDs = make([]uint64, 0)
		versionIDs = make([]uint64, 0)
		channelMap = make(map[uint64]*channelDao.Model)
		versionMap = make(map[uint64]*versionDao.Model)

		withFields   = make(showDao.ShowWithClassList, 0)
		withFieldMap = make(map[uint64]showDao.ShowWithClassList)
		fieldIDs     = make([]uint64, 0)
		fields       = make(classFieldDao.ModelList, 0)
		fieldMap     = make(map[uint64]classFieldDao.ModelList)
	)

	// 若有用户ID, 处理用户的限制

	showModel, err = e.ShowRepo.FetchByID(ctx, showID)
	if err != nil {
		return nil, err
	}
	if showModel.ID == 0 {
		return nil, ecode.NotFoundErr
	}

	i18nKeyList = append(i18nKeyList,
		showModel.NameKey, showModel.OverviewKey, showModel.AirDateKey)
	{
		// franchise
		if fID := showModel.FranchiseID; fID > 0 {
			franchises, err = e.FranchiseRepo.FindByFilter(ctx, &franchiseDao.Filter{
				ID: fID,
			})
			i18nKeyList = append(i18nKeyList, franchises.GetNameKeys()...)
		}

		// genres
		withGenres, err = e.ShowRepo.FindGenreByFilter(ctx, &showDao.GenreFilter{
			ShowID: showID,
		})
		genreIDs = withGenres.GetGenreIDs()

		if len(genreIDs) > 0 {
			genres, err = e.GenreRepo.FindByFilter(ctx, &genreDao.Filter{
				IDs: genreIDs,
			})
			i18nKeyList = append(i18nKeyList, genres.GetNameKeys()...)
		}

	}

	{
		withLimits, err = e.ShowRepo.FindLimitByFilter(ctx, &showDao.LimitFilter{
			ShowID: showID,
			Type:   uint32(showDao.LimitTypeChannel),
		})
		channelIDs = withLimits.GetChannelIDs()
		versionIDs = withLimits.GetVersionIDs()

		channels, err := resource.GetService().GetChannels(ctx, channelIDs)
		if err != nil {
			return nil, err
		}
		channelMap = channels.GetIDMap()

		versions, err := resource.GetService().GetVersions(ctx, versionIDs)
		if err != nil {
			return nil, err
		}
		versionMap = versions.GetIDMap()
	}
	{
		// with fields
		withFields, err = e.ShowRepo.FindClassByFilter(ctx, &showDao.ClassFilter{
			ShowID: showID,
		})
		if err != nil {
			return nil, err
		}
		fieldIDs = withFields.GetFieldIDs()
		withFieldMap = withFields.GetShowIDMap()

		// fields
		fields, err = e.ClassFieldRepo.FindByFilter(ctx, &classFieldDao.Filter{
			IDs: fieldIDs,
		})
		i18nKeyList = append(i18nKeyList, fields.GetNameKeys()...)
		fieldMap = fields.GetIDMap()
	}

	credits, err = e.CreditRepo.FindByFilter(ctx, &creditDao.Filter{
		ShowID: showID,
	})
	personIDs = credits.GetPersonIDs()

	if len(personIDs) > 0 {
		persons, err = e.PersonRepo.FindByFilter(ctx, &personDao.Filter{
			IDs: personIDs,
		})

		imageIDs = append(imageIDs, persons.GetImageIDs()...)
	}

	//seasons, err = e.SeasonRepo.FindByFilter(ctx, &seasonDao.Filter{
	//	ShowID: showID,
	//})

	posters, err = e.PosterRepo.FindByFilter(ctx, &posterDao.Filter{
		ShowID: showID,
	})

	imageIDs = append(imageIDs, posters.GetImageIDs()...)

	if len(imageIDs) > 0 {
		images, err = e.ImageRepo.FindByFilter(ctx, &imageDao.Filter{
			IDs: imageIDs,
		})
	}

	episodes, err = e.EpisodeRepo.FindByFilter(ctx, &episodeDao.Filter{
		ShowID: showID,
	})

	// 校验ISO_639_1
	if len(i18nKeyList) > 0 {
		i18nKeys, err = e.I18nRepo.FindByFilter(ctx, &i18nDao.Filter{
			Keys: i18nKeyList,
			//ISO_639_1: req.ISO_639_1,
		})
	}

	i18nKey := i18nKeys.GetI18nKey()

	e.setAdminShowDetailResp(
		req,
		resp,
		showModel,
		credits,
		episodes,
		posters,
		persons,
		images,
		genres,
		withGenres,
		franchises,
		i18nKey,
		channelKeyInfo,
	)

	resp.ShowBase.Name = resp.ShowBase.OriginalName

	showEpisodeMap, err := e.getAdminShowEpisodeInfo(ctx, []uint64{req.ID})
	if err != nil {
		return resp, nil
	}
	if len(showEpisodeMap) > 0 {
		if episodeCount, ok := showEpisodeMap[req.ID]; ok {
			resp.NumberOfEpisodes = uint32(episodeCount)
		}
	}

	if len(channelMap) > 0 {
		for _, chnl := range channelMap {
			resp.Channels = append(resp.Channels, &resourceDto.AdminChannelListItem{
				ID:   chnl.ID,
				Name: chnl.Name,
			})
		}
	}
	if len(versionMap) > 0 {
		for _, version := range versionMap {
			resp.Versions = append(resp.Versions, &resourceDto.AdminVersionListItem{
				ID:   version.ID,
				Name: version.Name,
			})
		}
	}
	if len(fieldMap) > 0 {
		if resp.Classes == nil || len(resp.Classes) <= 0 {
			resp.Classes = make([]*showDto.Class, 0)
		}
		if withs, ok := withFieldMap[showID]; ok {
			for _, with := range withs {
				if fields, ok := fieldMap[with.FieldID]; ok {
					for _, field := range fields {
						class := &showDto.Class{
							FieldID: field.ID,
							ClassID: field.ClassID,
							Name:    field.Name,
						}

						if i18n, ok := i18nKey[field.NameKey]; ok {
							for _, n := range i18n {
								if n.ISO_639_1 == req.ISO_639_1 {
									class.Name = n.Value
								}
							}
						}

						resp.Classes = append(resp.Classes, class)
					}
				}
			}
		}
	}

	return
}

func (e Entry) AdminShowCreate(ctx *gin.Context, req *showDto.AdminShowCreateReq, txOption ...*gorm.DB) (resp *showDto.AdminShowCreateResp, err error) {
	resp = &showDto.AdminShowCreateResp{}

	var (
		showModel    = &showDao.Show{}
		imageModels  = make([]*imageDao.Image, 0)
		posterModels = make([]*posterDao.Poster, 0)
		creditModels = make([]*creditDao.Credit, 0)

		withGenres  = make(showDao.ShowWithGenreList, 0)
		withClasses = make(showDao.ShowWithClassList, 0)
		tx          *gorm.DB
		isNewTx     bool
	)
	if len(txOption) > 0 {
		tx = txOption[0]
	} else {
		isNewTx = true
		tx = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	}

	// []string to string, comma separated
	fn := func(ori []string) string {
		return strings.Join(ori, ",")
	}

	showModel.Name = req.Name
	showModel.NameKey = req.NameKey
	showModel.Overview = req.OverView
	showModel.OverviewKey = req.OverViewKey
	showModel.ContentType = showDao.ContentType(req.ContentType)
	showModel.AirDate = req.AirDate
	showModel.AirDateKey = req.AirDateKey
	showModel.Score = req.Score
	showModel.FranchiseID = req.Franchise
	showModel.Homepage = req.Homepage
	showModel.InProduction = showDao.InProduction(req.InProduction)
	showModel.Langs = fn(req.Langs) // []string to string
	showModel.PresentationTime = req.PresentationTime
	//showModel.Images = req.Images
	//showModel.Credits = req.Credits

	showModel.Status = uint32(dbs.StatusDisable)

	if nameKey := req.NameKey; nameKey != "" {
		err = e.I18nRepo.UpdateMapByFilterWithTx(ctx, tx, &i18nDao.Filter{
			Key: req.NameKey,
		}, map[string]interface{}{
			"table":  "content_show",
			"column": "name",
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	if overviewKey := req.OverViewKey; overviewKey != "" {
		err = e.I18nRepo.UpdateMapByFilterWithTx(ctx, tx, &i18nDao.Filter{
			Key: req.OverViewKey,
		}, map[string]interface{}{
			"table":  "content_show",
			"column": "overview",
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	if airDateKey := req.AirDateKey; airDateKey != "" {
		err = e.I18nRepo.UpdateMapByFilterWithTx(ctx, tx, &i18nDao.Filter{
			Key: req.AirDateKey,
		}, map[string]interface{}{
			"table":  "content_show",
			"column": "air_date",
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	showID, err := e.ShowRepo.CreateWithTx(ctx, tx, showModel)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	resp.ID = showID

	for _, image := range req.Images {
		imageModel := &imageDao.Image{
			AspectRatio: image.AspectRatio,
			Width:       image.Width,
			Height:      image.Height,
			FilePath:    dbs.OssFilePath(image.FilePath),
			ISO_639_1:   image.Iso6391,
			AspectType:  image.AspectType,
		}
		imageModels = append(imageModels, imageModel)
	}

	err = e.ImageRepo.BatchCreateWithTx(ctx, tx, imageModels)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	for _, model := range imageModels {
		posterModel := &posterDao.Poster{
			ShowID:    showID,
			ImageID:   model.ID,
			ISO_639_1: model.ISO_639_1,
		}
		posterModels = append(posterModels, posterModel)
	}

	err = e.PosterRepo.BatchCreateWithTx(ctx, tx, posterModels)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	for _, credit := range req.Credits {
		creditModel := &creditDao.Credit{
			PersonID:   credit.PersonID,
			ShowID:     showID,
			Job:        creditDao.CreditJob(credit.Job),
			Department: credit.Department,
			Character:  credit.Character,
			Order:      credit.Order,
		}
		creditModels = append(creditModels, creditModel)
	}

	err = e.CreditRepo.BatchCreateWithTx(ctx, tx, creditModels)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	for _, genre := range req.Genres {
		withGenre := &showDao.ShowWithGenre{
			ShowID:  showID,
			GenreID: genre,
		}
		withGenres = append(withGenres, withGenre)
	}

	err = e.ShowRepo.GenreBatchCreateWithTx(ctx, tx, withGenres)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	for _, class := range req.Classes {
		withClass := &showDao.ShowWithClass{
			ShowID:  showID,
			ClassID: class.ClassID,
			FieldID: class.FieldID,
		}
		withClasses = append(withClasses, withClass)
	}

	err = e.ShowRepo.ClassBatchCreateWithTx(ctx, tx, withClasses)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	if t := req.ChannelFilters; len(t) > 0 {
		withLimit := make(showDao.ShowWithLimitList, 0)
		for _, limit := range t {
			withLimit = append(withLimit, &showDao.ShowWithLimit{
				ShowID:  showID,
				LimitID: limit,
				Type:    showDao.LimitTypeChannel,
			})
		}

		err = e.ShowRepo.LimitBatchCreateWithTx(ctx, tx, withLimit)
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	//if t := req.VersionFilters; len(t) > 0 {
	//	withLimit := make(showDao.ShowWithLimitList, 0)
	//	for _, limit := range t {
	//		withLimit = append(withLimit, &showDao.ShowWithLimit{
	//			ShowID:  showID,
	//			LimitID: limit,
	//			Type:    showDao.LimitTypeVersion,
	//		})
	//	}
	//
	//	err = e.ShowRepo.LimitBatchCreateWithTx(ctx, tx, withLimit)
	//	if err != nil {
	//		tx.Rollback()
	//		return nil, err
	//	}
	//}

	//if t := req.AuditFilter; t > 0 {
	//	withLimit := make(showDao.ShowWithLimitList, 0)
	//	withLimit = append(withLimit, &showDao.ShowWithLimit{
	//		ShowID:  showID,
	//		LimitID: t,
	//		Type:    showDao.LimitTypeAudit,
	//	})
	//
	//	err = e.ShowRepo.LimitBatchCreateWithTx(ctx, tx, withLimit)
	//	if err != nil {
	//		tx.Rollback()
	//		return nil, err
	//	}
	//}

	if isNewTx {
		tx.Commit()
	}

	return
}

func (e Entry) AdminShowUpdate(ctx *gin.Context, req *showDto.AdminShowUpdateReq, txOption ...*gorm.DB) (res *showDto.AdminShowUpdateResp, err error) {
	res = &showDto.AdminShowUpdateResp{}

	var (
		showModel = &showDao.Show{}
		updateMap = make(map[string]interface{})
		tx        *gorm.DB
		isNewTx   bool
	)

	showModel, err = e.ShowRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	if len(txOption) > 0 {
		tx = txOption[0]
	} else {
		tx = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
		isNewTx = true
	}

	if req.Name != "" && req.Name != showModel.Name {
		updateMap["name"] = req.Name
	}
	if req.NameKey != "" && req.NameKey != showModel.NameKey {
		updateMap["name_key"] = req.NameKey
	}
	if req.OverView != "" && req.OverView != showModel.Overview {
		updateMap["overview"] = req.OverView
	} else if req.OverView == "" {
		updateMap["overview"] = req.OverView
	}
	if req.OverViewKey != "" && req.OverViewKey != showModel.OverviewKey {
		updateMap["overview_key"] = req.OverViewKey
	}
	if req.ContentType != 0 && req.ContentType != uint32(showModel.ContentType) {
		updateMap["content_type"] = req.ContentType
	} else if req.ContentType == 0 {
		updateMap["content_type"] = 0
	}

	if req.Score != 0 && req.Score != showModel.Score {
		updateMap["score"] = req.Score
	} else if req.Score == 0 {
		updateMap["score"] = 0
	}
	if req.AirDate != "" && req.AirDate != showModel.AirDate {
		updateMap["air_date"] = req.AirDate
	} else if req.AirDate == "" {
		updateMap["air_date"] = ""
	}

	if req.AirDateKey != "" && req.AirDateKey != showModel.AirDateKey {
		updateMap["air_date_key"] = req.AirDateKey
	}
	if req.Franchise != 0 && req.Franchise != showModel.FranchiseID {
		updateMap["franchise_id"] = req.Franchise
	} else if req.Franchise == 0 {
		updateMap["franchise_id"] = 0
	}
	if req.Homepage != "" && req.Homepage != showModel.Homepage {
		updateMap["homepage"] = req.Homepage
	} else if req.Homepage == "" {
		updateMap["homepage"] = ""
	}
	if req.InProduction != 0 && req.InProduction != showModel.InProduction.Uint32() {
		updateMap["in_production"] = req.InProduction
	} else if req.InProduction == 0 {
		updateMap["in_production"] = 0
	}
	if len(req.Langs) > 0 {
		updateMap["langs"] = strings.Join(req.Langs, ",")
	} else {
		updateMap["langs"] = ""
	}

	if len(req.Genres) > 0 {
		// 删除原有的withGenres
		_, err = e.ShowRepo.DeleteGenreByFilterWithTx(ctx, tx, &showDao.GenreFilter{
			ShowID: req.ID,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}

		// 构造新的withGenres
		withGenres := make(showDao.ShowWithGenreList, 0)
		for _, genre := range req.Genres {
			withGenre := &showDao.ShowWithGenre{
				ShowID:  req.ID,
				GenreID: genre,
			}
			withGenres = append(withGenres, withGenre)
		}

		// 创建新的withGenres
		err = e.ShowRepo.GenreBatchCreateWithTx(ctx, tx, withGenres)
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	} else if len(req.Genres) == 0 {
		_, err = e.ShowRepo.DeleteGenreByFilterWithTx(ctx, tx, &showDao.GenreFilter{
			ShowID: req.ID,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	// classes
	if len(req.Classes) > 0 {
		// 删除原有的withGenres
		_, err = e.ShowRepo.DeleteClassByFilterWithTx(ctx, tx, &showDao.ClassFilter{
			ShowID: req.ID,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}

		// 构造新的withClasses
		withClasses := make(showDao.ShowWithClassList, 0)
		for _, class := range req.Classes {
			withGenre := &showDao.ShowWithClass{
				ShowID:  req.ID,
				ClassID: class.ClassID,
				FieldID: class.FieldID,
			}
			withClasses = append(withClasses, withGenre)
		}

		// 创建新的withGenres
		err = e.ShowRepo.ClassBatchCreateWithTx(ctx, tx, withClasses)
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	} else if len(req.Classes) == 0 {
		_, err = e.ShowRepo.DeleteClassByFilterWithTx(ctx, tx, &showDao.ClassFilter{
			ShowID: req.ID,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	if len(req.Images) > 0 {

		_, err = e.PosterRepo.DeleteByFilterWithTx(ctx, tx, &posterDao.Filter{
			ShowID: req.ID,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}

		var (
			imageModels  = make([]*imageDao.Image, 0)
			posterModels = make([]*posterDao.Poster, 0)
		)

		for _, image := range req.Images {
			imageModel := &imageDao.Image{
				AspectRatio: image.AspectRatio,
				Width:       image.Width,
				Height:      image.Height,
				FilePath:    dbs.OssFilePath(image.FilePath),
				ISO_639_1:   image.Iso6391,
				AspectType:  image.AspectType,
			}
			imageModels = append(imageModels, imageModel)
		}

		err = e.ImageRepo.BatchCreateWithTx(ctx, tx, imageModels)
		if err != nil {
			tx.Rollback()
			return nil, err
		}

		for _, model := range imageModels {
			posterModel := &posterDao.Poster{
				ShowID:    req.ID,
				ImageID:   model.ID,
				ISO_639_1: model.ISO_639_1,
			}
			posterModels = append(posterModels, posterModel)
		}

		err = e.PosterRepo.BatchCreateWithTx(ctx, tx, posterModels)
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	} else if len(req.Images) == 0 {
		_, err = e.PosterRepo.DeleteByFilterWithTx(ctx, tx, &posterDao.Filter{
			ShowID: req.ID,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	if len(req.Credits) > 0 {
		// 删除原有的credits
		_, err = e.CreditRepo.DeleteByFilterWithTx(ctx, tx, &creditDao.Filter{
			ShowID: req.ID,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}

		// 构造新的credits
		creditModels := make([]*creditDao.Credit, 0)
		for _, credit := range req.Credits {
			creditModel := &creditDao.Credit{
				PersonID:   credit.PersonID,
				ShowID:     req.ID,
				Job:        creditDao.CreditJob(credit.Job),
				Department: credit.Department,
				Character:  credit.Character,
				Order:      credit.Order,
			}
			creditModels = append(creditModels, creditModel)
		}

		// 创建新的credits
		err = e.CreditRepo.BatchCreateWithTx(ctx, tx, creditModels)
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	} else if len(req.Credits) == 0 {
		_, err = e.CreditRepo.DeleteByFilterWithTx(ctx, tx, &creditDao.Filter{
			ShowID: req.ID,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	if req.NameKey != "" && req.NameKey != showModel.NameKey {
		err = e.I18nRepo.UpdateMapByFilterWithTx(ctx, tx, &i18nDao.Filter{
			Key: req.NameKey,
		}, map[string]interface{}{
			"table":  "content_show",
			"column": "name",
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	if req.OverViewKey != "" && req.OverViewKey != showModel.OverviewKey {
		err = e.I18nRepo.UpdateMapByFilterWithTx(ctx, tx, &i18nDao.Filter{
			Key: req.OverViewKey,
		}, map[string]interface{}{
			"table":  "content_show",
			"column": "overview",
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	if req.AirDateKey != "" && req.AirDateKey != showModel.AirDateKey {
		err = e.I18nRepo.UpdateMapByFilterWithTx(ctx, tx, &i18nDao.Filter{
			Key: req.AirDateKey,
		}, map[string]interface{}{
			"table":  "content_show",
			"column": "air_date",
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	err = e.ShowRepo.UpdateMapByIDWithTx(ctx, tx, req.ID, updateMap)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	if chans := req.ChannelFilters; len(chans) > 0 {

		// 删除原有的withLimits
		_, err = e.ShowRepo.DeleteLimitByFilterWithTx(ctx, tx, &showDao.LimitFilter{
			ShowID: req.ID,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}

		withLimit := make(showDao.ShowWithLimitList, 0)
		for _, limit := range chans {
			withLimit = append(withLimit, &showDao.ShowWithLimit{
				ShowID:  req.ID,
				LimitID: limit,
				Type:    showDao.LimitTypeChannel,
			})
		}

		err = e.ShowRepo.LimitBatchCreateWithTx(ctx, tx, withLimit)
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	} else if len(chans) == 0 {
		// 删除原有的withLimits
		_, err = e.ShowRepo.DeleteLimitByFilterWithTx(ctx, tx, &showDao.LimitFilter{
			ShowID: req.ID,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	if chans := req.ChannelFilters; len(chans) > 0 {

		// 删除原有的withLimits
		_, err = e.ShowRepo.DeleteLimitByFilterWithTx(ctx, tx, &showDao.LimitFilter{
			ShowID: req.ID,
			Type:   uint32(showDao.LimitTypeChannel),
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}

		withLimit := make(showDao.ShowWithLimitList, 0)
		for _, limit := range chans {
			withLimit = append(withLimit, &showDao.ShowWithLimit{
				ShowID:  req.ID,
				LimitID: limit,
				Type:    showDao.LimitTypeChannel,
			})
		}

		err = e.ShowRepo.LimitBatchCreateWithTx(ctx, tx, withLimit)
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	} else if len(chans) == 0 {
		// 删除原有的withLimits
		_, err = e.ShowRepo.DeleteLimitByFilterWithTx(ctx, tx, &showDao.LimitFilter{
			ShowID: req.ID,
			Type:   uint32(showDao.LimitTypeChannel),
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	//if vers := req.VersionFilters; len(vers) > 0 {
	//
	//	// 删除原有的withLimits
	//	_, err = e.ShowRepo.DeleteLimitByFilterWithTx(ctx, tx, &showDao.LimitFilter{
	//		ShowID: req.ID,
	//		Type:   uint32(showDao.LimitTypeVersion),
	//	})
	//	if err != nil {
	//		tx.Rollback()
	//		return nil, err
	//	}
	//
	//	withLimit := make(showDao.ShowWithLimitList, 0)
	//	for _, limit := range vers {
	//		withLimit = append(withLimit, &showDao.ShowWithLimit{
	//			ShowID:  req.ID,
	//			LimitID: limit,
	//			Type:    showDao.LimitTypeVersion,
	//		})
	//	}
	//
	//	err = e.ShowRepo.LimitBatchCreateWithTx(ctx, tx, withLimit)
	//	if err != nil {
	//		tx.Rollback()
	//		return nil, err
	//	}
	//} else if len(vers) == 0 {
	//	// 删除原有的withLimits
	//	_, err = e.ShowRepo.DeleteLimitByFilterWithTx(ctx, tx, &showDao.LimitFilter{
	//		ShowID: req.ID,
	//		Type:   uint32(showDao.LimitTypeVersion),
	//	})
	//	if err != nil {
	//		tx.Rollback()
	//		return nil, err
	//	}
	//}

	//if t := req.AuditFilter; t > 0 {
	//	withLimit := make(showDao.ShowWithLimitList, 0)
	//	withLimit = append(withLimit, &showDao.ShowWithLimit{
	//		ShowID:  req.ID,
	//		LimitID: t,
	//		Type:    showDao.LimitTypeAudit,
	//	})
	//
	//	err = e.ShowRepo.LimitBatchCreateWithTx(ctx, tx, withLimit)
	//	if err != nil {
	//		tx.Rollback()
	//		return nil, err
	//	}
	//}

	if isNewTx {
		tx.Commit()
	}

	return res, nil
}

func (e Entry) ImportShowUpdate(ctx *gin.Context, req *showDto.AdminShowUpdateReq, txOption ...*gorm.DB) (res *showDto.AdminShowUpdateResp, err error) {
	res = &showDto.AdminShowUpdateResp{}

	var (
		showModel = &showDao.Show{}
		updateMap = make(map[string]interface{})
		tx        *gorm.DB
		isNewTx   bool
	)

	showModel, err = e.ShowRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	if len(txOption) > 0 {
		tx = txOption[0]
	} else {
		tx = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
		isNewTx = true
	}

	// 批量导入时不更新主表name overview
	//if req.Name != "" && req.Name != showModel.Name {
	//	updateMap["name"] = req.Name
	//}
	if req.NameKey != "" && req.NameKey != showModel.NameKey {
		updateMap["name_key"] = req.NameKey
	}
	//if req.OverView != "" && req.OverView != showModel.Overview {
	//	updateMap["overview"] = req.OverView
	//} else if req.OverView == "" {
	//	updateMap["overview"] = req.OverView
	//}
	if req.OverViewKey != "" && req.OverViewKey != showModel.OverviewKey {
		updateMap["overview_key"] = req.OverViewKey
	}
	if req.ContentType != 0 && req.ContentType != uint32(showModel.ContentType) {
		updateMap["content_type"] = req.ContentType
	} else if req.ContentType == 0 {
		updateMap["content_type"] = 0
	}

	if req.Score != 0 && req.Score != showModel.Score {
		updateMap["score"] = req.Score
	} else if req.Score == 0 {
		updateMap["score"] = 0
	}
	if req.AirDate != "" && req.AirDate != showModel.AirDate {
		updateMap["air_date"] = req.AirDate
	} else if req.AirDate == "" {
		updateMap["air_date"] = ""
	}

	if req.AirDateKey != "" && req.AirDateKey != showModel.AirDateKey {
		updateMap["air_date_key"] = req.AirDateKey
	}
	if req.Franchise != 0 && req.Franchise != showModel.FranchiseID {
		updateMap["franchise_id"] = req.Franchise
	} else if req.Franchise == 0 {
		updateMap["franchise_id"] = 0
	}
	if req.Homepage != "" && req.Homepage != showModel.Homepage {
		updateMap["homepage"] = req.Homepage
	} else if req.Homepage == "" {
		updateMap["homepage"] = ""
	}
	if req.InProduction != 0 && req.InProduction != showModel.InProduction.Uint32() {
		updateMap["in_production"] = req.InProduction
	} else if req.InProduction == 0 {
		updateMap["in_production"] = 0
	}
	if len(req.Langs) > 0 {
		updateMap["langs"] = strings.Join(req.Langs, ",")
	} else {
		updateMap["langs"] = ""
	}

	if len(req.Genres) > 0 {
		// 删除原有的withGenres
		_, err = e.ShowRepo.DeleteGenreByFilterWithTx(ctx, tx, &showDao.GenreFilter{
			ShowID: req.ID,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}

		// 构造新的withGenres
		withGenres := make(showDao.ShowWithGenreList, 0)
		for _, genre := range req.Genres {
			withGenre := &showDao.ShowWithGenre{
				ShowID:  req.ID,
				GenreID: genre,
			}
			withGenres = append(withGenres, withGenre)
		}

		// 创建新的withGenres
		err = e.ShowRepo.GenreBatchCreateWithTx(ctx, tx, withGenres)
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	} else if len(req.Genres) == 0 {
		_, err = e.ShowRepo.DeleteGenreByFilterWithTx(ctx, tx, &showDao.GenreFilter{
			ShowID: req.ID,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	// classes
	if len(req.Classes) > 0 {
		// 删除原有的withGenres
		_, err = e.ShowRepo.DeleteClassByFilterWithTx(ctx, tx, &showDao.ClassFilter{
			ShowID: req.ID,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}

		// 构造新的withClasses
		withClasses := make(showDao.ShowWithClassList, 0)
		for _, class := range req.Classes {
			withGenre := &showDao.ShowWithClass{
				ShowID:  req.ID,
				ClassID: class.ClassID,
				FieldID: class.FieldID,
			}
			withClasses = append(withClasses, withGenre)
		}

		// 创建新的withGenres
		err = e.ShowRepo.ClassBatchCreateWithTx(ctx, tx, withClasses)
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	} else if len(req.Classes) == 0 {
		_, err = e.ShowRepo.DeleteClassByFilterWithTx(ctx, tx, &showDao.ClassFilter{
			ShowID: req.ID,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	if len(req.Images) > 0 {

		_, err = e.PosterRepo.DeleteByFilterWithTx(ctx, tx, &posterDao.Filter{
			ShowID:    req.ID,
			ISO_639_1: req.Iso_639_1,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}

		var (
			imageModels  = make([]*imageDao.Image, 0)
			posterModels = make([]*posterDao.Poster, 0)
		)

		for _, image := range req.Images {
			imageModel := &imageDao.Image{
				AspectRatio: image.AspectRatio,
				Width:       image.Width,
				Height:      image.Height,
				FilePath:    dbs.OssFilePath(image.FilePath),
				ISO_639_1:   image.Iso6391,
				AspectType:  image.AspectType,
			}
			imageModels = append(imageModels, imageModel)
		}

		err = e.ImageRepo.BatchCreateWithTx(ctx, tx, imageModels)
		if err != nil {
			tx.Rollback()
			return nil, err
		}

		for _, model := range imageModels {
			posterModel := &posterDao.Poster{
				ShowID:    req.ID,
				ImageID:   model.ID,
				ISO_639_1: model.ISO_639_1,
			}
			posterModels = append(posterModels, posterModel)
		}

		err = e.PosterRepo.BatchCreateWithTx(ctx, tx, posterModels)
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	} else if len(req.Images) == 0 {
		_, err = e.PosterRepo.DeleteByFilterWithTx(ctx, tx, &posterDao.Filter{
			ShowID:    req.ID,
			ISO_639_1: req.Iso_639_1,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	if len(req.Credits) > 0 {
		// 删除原有的credits
		_, err = e.CreditRepo.DeleteByFilterWithTx(ctx, tx, &creditDao.Filter{
			ShowID: req.ID,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}

		// 构造新的credits
		creditModels := make([]*creditDao.Credit, 0)
		for _, credit := range req.Credits {
			creditModel := &creditDao.Credit{
				PersonID:   credit.PersonID,
				ShowID:     req.ID,
				Job:        creditDao.CreditJob(credit.Job),
				Department: credit.Department,
				Character:  credit.Character,
				Order:      credit.Order,
			}
			creditModels = append(creditModels, creditModel)
		}

		// 创建新的credits
		err = e.CreditRepo.BatchCreateWithTx(ctx, tx, creditModels)
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	} else if len(req.Credits) == 0 {
		_, err = e.CreditRepo.DeleteByFilterWithTx(ctx, tx, &creditDao.Filter{
			ShowID: req.ID,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	if req.NameKey != "" {
		err = e.I18nRepo.UpdateMapByFilterWithTx(ctx, tx, &i18nDao.Filter{
			Key: req.NameKey,
		}, map[string]interface{}{
			"table":  "content_show",
			"column": "name",
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	if req.OverViewKey != "" {
		err = e.I18nRepo.UpdateMapByFilterWithTx(ctx, tx, &i18nDao.Filter{
			Key: req.OverViewKey,
		}, map[string]interface{}{
			"table":  "content_show",
			"column": "overview",
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	if req.AirDateKey != "" {
		err = e.I18nRepo.UpdateMapByFilterWithTx(ctx, tx, &i18nDao.Filter{
			Key: req.AirDateKey,
		}, map[string]interface{}{
			"table":  "content_show",
			"column": "air_date",
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
	}

	err = e.ShowRepo.UpdateMapByIDWithTx(ctx, tx, req.ID, updateMap)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	if isNewTx {
		tx.Commit()
	}

	return res, nil
}

func (e *Entry) AdminShowStatusBatch(ctx *gin.Context, req *showDto.AdminShowStatusBatchReq) (res *showDto.AdminShowStatusBatchResp, err error) {
	res = &showDto.AdminShowStatusBatchResp{}

	if len(req.IDs) == 0 {
		return res, nil
	}

	var (
		ids = make([]uint64, 0)
	)
	for _, d := range req.IDs {
		if d == 0 {
			continue
		}
		ids = append(ids, d)
	}
	if len(ids) == 0 {
		return res, nil
	}

	err = e.ShowRepo.UpdateMapByFilter(ctx, &showDao.Filter{
		IDs: ids,
	}, map[string]interface{}{
		"status": req.Status,
	})
	if err != nil {
		return nil, err
	}

	return res, nil
}

func (e Entry) AdminShowUpdatePatch(ctx *gin.Context, req *showDto.AdminShowUpdatePatchReq) (resp *showDto.AdminShowUpdatePatchResp, err error) {
	resp = &showDto.AdminShowUpdatePatchResp{}

	var (
		showModel = &showDao.Show{}
		updateMap = make(map[string]interface{})
	)

	showModel, err = e.ShowRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	if showModel.Status == uint32(dbs.StatusDisable) {
		updateMap["status"] = uint32(dbs.StatusEnable)
	} else {
		updateMap["status"] = uint32(dbs.StatusDisable)
	}

	err = e.ShowRepo.UpdateMapByIDWithTx(ctx, dbs.NewMysqlEngines().UseWithGinCtx(ctx, true), req.ID, updateMap)
	if err != nil {
		return nil, err
	}

	return
}

func (e Entry) AdminShowDelete(ctx *gin.Context, req *showDto.AdminShowDeleteReq) (resp *showDto.AdminShowDeleteResp, err error) {
	resp = &showDto.AdminShowDeleteResp{}

	var (
		showModel = &showDao.Show{}
		updateMap = make(map[string]interface{})
	)

	showModel, err = e.ShowRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	if showModel.IsDeleted == dbs.True {
	} else {
		updateMap["is_deleted"] = dbs.True
	}

	err = e.ShowRepo.UpdateMapByIDWithTx(ctx, dbs.NewMysqlEngines().UseWithGinCtx(ctx, true), req.ID, updateMap)
	if err != nil {
		return nil, err
	}

	return
}

func (e Entry) AdminShowTmdb(ctx *gin.Context, req *showDto.AdminShowTmdbReq) (resp *showDto.AdminShowTmdbResp, err error) {
	resp = &showDto.AdminShowTmdbResp{}

	var (
		page = req.Page
	)

	// 若输入关键词, 则请求tmdb的HTTP接口
	if req.Name != "" {
		if page == 0 {
			page = 1
		}
		var ret *tmdb.TmdbList
		ret, err = tmdb.SearchTmdbList(req.Name, uint64(page))
		if err != nil {
			return nil, err
		}
		resp.TmdbList = ret
		return
	}

	if req.TmdbID != 0 {
	}

	return
}

func (e Entry) AdminShowTmdbDetail(ctx *gin.Context, req *showDto.AdminShowTmdbDetailReq) (resp *showDto.AdminShowTmdbDetailResp, err error) {
	resp = &showDto.AdminShowTmdbDetailResp{}

	var (
		tmdbID = req.ID
	)

	// 若输入关键词, 则请求tmdb的HTTP接口
	if tmdbID != 0 {

	}

	return
}

func (e Entry) AdminShowTmdbUrl(ctx *gin.Context, req *showDto.AdminShowTmdbUrlReq) (resp *showDto.AdminShowTmdbUrlResp, err error) {
	resp = &showDto.AdminShowTmdbUrlResp{}

	return
}

// 定义视频结构体
type Video struct {
	Name string
	URL  string
}

func toVideo(rawString string) []Video {
	// 结果存储
	videos := make([]Video, 0, 100) // 预分配切片容量

	// 遍历字符串
	start := 0
	for start < len(rawString) {
		// 找到 `$` 位置
		dollarIdx := strings.IndexByte(rawString[start:], '$')
		if dollarIdx == -1 {
			break
		}
		dollarIdx += start

		// 找到 `#` 位置
		hashIdx := strings.IndexByte(rawString[dollarIdx:], '#')
		if hashIdx == -1 {
			hashIdx = len(rawString) // 如果没有 `#`，取到字符串末尾
		} else {
			hashIdx += dollarIdx
		}

		// 提取 `name` 和 `URL`
		name := rawString[start:dollarIdx]
		url := rawString[dollarIdx+1 : hashIdx]

		// 存入切片
		videos = append(videos, Video{
			Name: name,
			URL:  url,
		})

		// 移动起始索引
		start = hashIdx + 1
	}

	return videos
}
