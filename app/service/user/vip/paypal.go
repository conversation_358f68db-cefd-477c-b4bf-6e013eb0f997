package vip

import (
	"vlab/app/common/dbs"
	"vlab/app/dao/common"
	"vlab/app/dao/user"
	vipBillDao "vlab/app/dao/user/vip_bill"
	payLog "vlab/app/dao/vip/pay_log"
	payLogDao "vlab/app/dao/vip/pay_log"
	userDto "vlab/app/dto/user"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"
	"vlab/pkg/redis"
	"vlab/pkg/util/timeUtil"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/paypal"
	"github.com/gogf/gf/v2/util/gconv"
)

// PaypalTransactionCreate .
func (e *Entry) PaypalTransactionCreate(ctx *gin.Context, uid uint64, req userDto.ThirdTransactionCreateReq) (*userDto.PaypalTransactionCreateResp, error) {
	if err := e.RedisCli.Lock(ctx, redis.GetUserPaypalTranceCreate(uid, req.ID), redis.LockTimeHalfMinute); err != nil {
		return nil, ecode.UserPaypalServerBusyErr
	}
	defer e.RedisCli.Unlock(ctx, redis.GetUserPaypalTranceCreate(uid, req.ID))

	pInfo, err := e.ProductRepo.RedisIDInfo(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	uInfo, err := e.UserSrv.GetUserInfo(ctx, uid)
	if err != nil {
		return nil, err
	}

	// 业务逻辑处理
	var (
		resp = &userDto.PaypalTransactionCreateResp{
			OrderRet: &paypal.OrderDetail{},
			SubRet:   &paypal.SubscriptionDetail{},
		}
		channelID, _      = helper.GetCtxChannelID(ctx)
		headers, _        = json.Marshal(ctx.Request.Header)
		date              = timeUtil.NowToDateStringByZone(ctx)
		logNo             = helper.GetAppNo(helper.AppNoVipOrder)
		logStatus         = uint32(payLogDao.StatusWaitPay)
		reqData, respData []byte
		transID, funcName string
		env               = e.AppStoreExec.GetCurrentEnv(ctx)
	)

	if pInfo.IsSub == dbs.False {
		orderRet, bm, err := e.PaypalExec.PaypalOrderCreate(ctx, uInfo, pInfo, logNo, req.SuccessURL, req.CancelURL)
		if err != nil {
			return nil, err
		}
		for _, links := range orderRet.Links {
			if links.Rel == "payer-action" {
				resp.PayerAction = links.Href
				break
			}
			continue
		}
		resp.OrderRet = orderRet
		reqData, _ = json.Marshal(bm)
		respData, _ = json.Marshal(orderRet)
		transID = orderRet.Id
		funcName = "CreateOrder"
	} else {
		subRet, bm, err := e.PaypalExec.PaypalSubscripeCreate(ctx, uInfo, pInfo, logNo, req.SuccessURL, req.CancelURL)
		if err != nil {
			return nil, err
		}
		for _, links := range subRet.Links {
			if links.Rel == "approve" {
				resp.Approve = links.Href
				break
			}
			continue
		}

		resp.SubRet = subRet
		reqData, _ = json.Marshal(bm)
		respData, _ = json.Marshal(subRet)
		transID = subRet.ID
		funcName = "SubscriptionCreate"
	}

	payLog := &payLogDao.Model{
		UserID:                uInfo.ID,
		Platform:              uint32(common.VipPlatPayPal),
		LogType:               uint32(payLogDao.LogRequest),
		Status:                logStatus,
		ProductID:             pInfo.ProductID,
		TransactionID:         transID,
		OriginalTransactionID: transID,
		PurchaseToken:         "",
		Amount:                int64(pInfo.Price),
		Currency:              pInfo.Currency,
		NotifyID:              "",
		NotifyType:            "client",
		SubType:               "",
		ExpiresTime:           0,
		Env:                   env,
		ChannelID:             channelID,
		TraceID:               helper.GetGinRequestID(ctx),
		Func:                  funcName,
		Date:                  date,
		Header:                string(headers),
		ReqData:               string(reqData),
		RespData:              string(respData),
	}

	if err := e.PayLogRepo.Create(ctx, payLog); err != nil {
		return nil, err
	}
	return resp, nil
}

// PaypalTransactionCheck .
func (e *Entry) PaypalTransactionCheck(ctx *gin.Context, uid uint64, req userDto.ThirdTransactionCheckReq) (*userDto.AdminUserListItem, error) {
	if err := e.RedisCli.Lock(ctx, redis.GetUserPaypalTranceCheck(uid, req.TranceID), redis.LockTimeHalfMinute); err != nil {
		return nil, ecode.UserPaypalServerBusyErr
	}
	defer e.RedisCli.Unlock(ctx, redis.GetUserPaypalTranceCheck(uid, req.TranceID))
	var (
		transId         = req.TranceID
		success         bool
		err             error
		env             = e.AppStoreExec.GetCurrentEnv(ctx)
		amount          int64
		currency        string
		lastPaymentTime string
	)

	if req.TranceType == "order" {
		orderDetail, err := e.PaypalExec.PaypalOrderCapture(ctx, req.TranceID)
		if err != nil {
			return nil, err
		}
		log.Ctx(ctx).WithField("orderDetail", orderDetail).Info("PaypalTransactionCheck PaypalSubscripeInfo")
		// TODO 是否需要捕获支付状态？
		if len(orderDetail.PurchaseUnits) == 0 {
			return nil, ecode.UserPaypalOrderNotFound
		}
		amount = gconv.Int64(gconv.Float64(orderDetail.PurchaseUnits[0].Amount.Value) * 1000)
		currency = orderDetail.PurchaseUnits[0].Amount.CurrencyCode

		if orderDetail.Status == "COMPLETED" {
			success = true
		}
	} else {
		// paypal/notify?subscription_id=I-BCT7XAPPMKK8&ba_token=BA-49S064102M4101948&token=6RL91944FV2634747
		if _, err = e.PaypalExec.PaypalSubscriptionCapture(ctx, req.TranceID); err != nil {
			return nil, err
		}
		subDetail, err := e.PaypalExec.PaypalSubscripeInfo(ctx, req.TranceID)
		if err != nil {
			return nil, err
		}
		log.Ctx(ctx).WithField("subDetail", subDetail).Info("PaypalTransactionCheck PaypalSubscripeInfo")

		amount = gconv.Int64(gconv.Float64(subDetail.BillingInfo.LastPayment.Amount.Value) * 1000)
		currency = subDetail.BillingInfo.LastPayment.Amount.CurrencyCode
		lastPaymentTime = subDetail.BillingInfo.LastPayment.Time

		if subDetail.Status == "ACTIVE" || subDetail.Status == "APPROVED" {
			success = true
		}
	}
	if !success {
		log.Ctx(ctx).WithField("success", success).Info("PaypalTransactionCheck Success")
	}

	logTransInfo, err := e.PayLogRepo.FeatchThirdToCheck(ctx, transId, uint32(payLogDao.StatusWaitPay))
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("e.PayLogRepo.FeatchThirdToCheck error")
		return nil, err
	}
	if logTransInfo.ID == dbs.False {
		return nil, ecode.UserPaypalTranceErr
	}

	uInfo, err := e.UserSrv.GetUserInfo(ctx, logTransInfo.UserID)
	if err != nil {
		return nil, err
	}

	// 业务逻辑处理
	var (
		channelID, _ = helper.GetCtxChannelID(ctx)
		date         = timeUtil.NowToDateStringByZone(ctx)
	)

	expireTime, gracePeriod, err := e.getProductExpireTime(ctx, uInfo.VipTime, logTransInfo.ProductID)
	if err != nil {
		return nil, err
	}

	vipBill := &vipBillDao.Model{
		UserID:     uInfo.ID,
		Platform:   uint32(common.VipPlatPayPal),
		LogID:      logTransInfo.ID,
		Amount:     amount,
		Currency:   currency,
		Env:        env,
		BillType:   vipBillDao.BtPayPalPay,
		Date:       date,
		BeforeTime: uInfo.VipTime,
		AfterTime:  expireTime,
		ChannelID:  channelID,
	}

	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	if err := func() (err error) {
		updateData := map[string]interface{}{
			"status":       payLogDao.StatusPaid,
			"amount":       amount,
			"currency":     currency,
			"expires_time": expireTime,
		}
		if lastPaymentTime != "" {
			updateData["sub_type"] = lastPaymentTime
		}
		if err = e.PayLogRepo.UpdateMapByIDWithTx(ctx, tx, logTransInfo.ID, updateData); err != nil {
			return
		}

		if err = e.VipBillRepo.CreateWithTx(ctx, tx, vipBill); err != nil {
			return
		}

		if err = e.UserRepo.UpdateMapByIDWithTx(ctx, tx, uInfo.ID, map[string]interface{}{
			"vip_time":     expireTime,
			"grace_period": gracePeriod,
		}); err != nil {
			return
		}

		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("PaypalTransactionCheck err")
		return nil, err
	}

	uInfo.VipTime = expireTime
	return uInfo.ToUserInfoResp(ctx), nil
}

// PaypalNotify .
/*
Payments: 支付(done)
Batch payouts: 批量支付
Billing plans and agreements: 账单计划和协议
Log in with PayPal: 使用 PayPal 登录
Checkout buyer approval: 结账买家批准
Disputes: 争议
Invoicing: 开票
Marketplaces and Platforms: 市场和平台
Merchant onboarding: 商户入职
Orders: 订单(done)
Payment orders: 支付订单(done)
Referenced payouts: 参考支付
Sales: 销售
Subscriptions 订阅(done)
Payment Method Tokens
*/
func (e *Entry) PaypalNotify(ctx *gin.Context, event paypal.WebhookEvent) error {
	log.Ctx(ctx).WithField("PaypalNotifyEventType", event.EventType).
		WithField("PaypalNotifyEventResourceType", event.ResourceType).
		WithField("event", event).
		WithField("eventResource", string(event.Resource)).
		Error("PaypalNotifyEvent")
	var (
		headers, _ = json.Marshal(ctx.Request.Header)
		reqData, _ = json.Marshal(event)
		date       = timeUtil.NowToDateStringByZone(ctx)
		env        = e.AppStoreExec.GetCurrentEnv(ctx)

		logTransInfo         = &payLogDao.Model{}
		logStatus            = uint32(payLogDao.StatusIgnore)
		updateUser      bool = false
		logUpdate       bool = false
		userId          uint64
		productId       string
		err             error
		amount          int64
		currency        string
		lastPaymentTime string
		tranceId        string
	)

	switch event.EventType {
	// 支付授权事件：支付授权创建, 支付授权失效
	case "PAYMENT.AUTHORIZATION.CREATED", "PAYMENT.AUTHORIZATION.VOIDED":
		updateUser, logStatus = false, uint32(payLogDao.StatusIgnore)
	// 支付完成
	case "PAYMENT.CAPTURE.COMPLETED":
		updateUser, logStatus = false, uint32(payLogDao.StatusIgnore)
	// 支付被拒绝, 支付待处理, 支付已退款 , 支付部分退款
	case "PAYMENT.CAPTURE.DENIED", "PAYMENT.CAPTURE.PENDING", "PAYMENT.CAPTURE.REFUNDED", "PAYMENT.CAPTURE.PARTIALLY_REFUNDED",
		// 支付已撤销, 退款被拒绝, 退款待处理, 退款完成, 支付捕获被拒绝(拒绝的授权付款)
		"PAYMENT.CAPTURE.REVERSED", "PAYMENT.REFUND.DENIED", "PAYMENT.REFUND.PENDING", "PAYMENT.REFUND.COMPLETED", "PAYMENT.CAPTURE.DECLINED":
		updateUser, logStatus = false, uint32(payLogDao.StatusIgnore)
	// 支付订单取消, 支付订单创建
	case "PAYMENT.ORDER.CANCELLED", "PAYMENT.ORDER.CREATED	":
		updateUser, logStatus = false, uint32(payLogDao.StatusIgnore)
	// 订单已批准, 订单已处理
	case "CHECKOUT.ORDER.APPROVED", "CHECKOUT.ORDER.PROCESSED":
		updateUser, logStatus = false, uint32(payLogDao.StatusIgnore)
	// 订单已完成
	case "CHECKOUT.ORDER.COMPLETED":
		eventResouce := &paypal.OrderDetail{}
		if err := json.Unmarshal(event.Resource, eventResouce); err != nil {
			return err
		}
		if eventResouce.Id == "" {
			return ecode.UserPaypalNotifyOrderNotFound
		}
		if logTransInfo, err = e.PayLogRepo.FeatchByTransId(ctx, eventResouce.Id, payLog.StatusPaid); err != nil {
			log.Ctx(ctx).WithError(err).Error("e.PayLogRepo.FeatchByTransId error")
			return err
		}
		if logTransInfo.ID == dbs.False {
			return ecode.UserPaypalNotifyOrderVerifyErr
		}
		if logTransInfo.Status == uint32(payLogDao.StatusWaitPay) {
			logUpdate, updateUser, userId, productId = true, true, logTransInfo.UserID, logTransInfo.ProductID

			amount = gconv.Int64(gconv.Float64(eventResouce.PurchaseUnits[0].Amount.Value) * 1000)
			currency = eventResouce.PurchaseUnits[0].Amount.CurrencyCode
		}

	/*=======================================================*/
	// 创建产品, 更新产品
	case "CATALOG.PRODUCT.CREATED", "CATALOG.PRODUCT.UPDATED":
		return nil
	// 订阅退款, 撤销订阅付款
	case "PAYMENT.SALE.REFUNDED", "PAYMENT.SALE.REVERSED":
		updateUser, logStatus = false, uint32(payLogDao.StatusIgnore)
	// 订阅计划创建, 更新, 激活, 暂定, 改价
	case "BILLING.PLAN.CREATED", "BILLING.PLAN.UPDATED", "BILLING.PLAN.ACTIVATED",
		"BILLING.PLAN.DEACTIVATED", "BILLING.PLAN.PRICING-CHANGE.ACTIVATED":
		return nil
	// 订阅支付  TODO 单独处理
	case "PAYMENT.SALE.COMPLETED":
		updateUser, logStatus = false, uint32(payLogDao.StatusIgnore)
	// 订阅创建, 激活,  更新
	case "BILLING.SUBSCRIPTION.CREATED", "BILLING.SUBSCRIPTION.ACTIVATED", "BILLING.SUBSCRIPTION.UPDATED":
		return nil
	// 订阅过期, 取消, 暂停
	case "BILLING.SUBSCRIPTION.EXPIRED", "BILLING.SUBSCRIPTION.CANCELLED", "BILLING.SUBSCRIPTION.SUSPENDED":
		updateUser, logStatus = false, uint32(payLogDao.StatusIgnore)
	// 订阅支付失败
	case "BILLING.SUBSCRIPTION.PAYMENT.FAILED": // TODO 单独处理
		updateUser, logStatus = false, uint32(payLogDao.StatusIgnore)

	// 订阅已续订
	case "BILLING.SUBSCRIPTION.RENEWED":
		eventResouce := &paypal.SubscriptionDetail{}
		if err := json.Unmarshal(event.Resource, eventResouce); err != nil {
			return err
		}
		if eventResouce.ID == "" {
			return ecode.UserPaypalSubNotFound
		}
		if logTransInfo, err = e.PayLogRepo.FeatchByTransId(ctx, eventResouce.ID, payLog.StatusPaid); err != nil {
			log.Ctx(ctx).WithError(err).Error("e.PayLogRepo.FeatchByTransId error")
			return err
		}
		if logTransInfo.ID == dbs.False {
			return ecode.UserPaypalNotifySubVerifyErr
		}

		amount = gconv.Int64(gconv.Float64(eventResouce.BillingInfo.LastPayment.Amount.Value) * 1000)
		currency = eventResouce.BillingInfo.LastPayment.Amount.CurrencyCode
		lastPaymentTime = eventResouce.BillingInfo.LastPayment.Time
		tranceId = eventResouce.ID

		if logTransInfo.SubType == "" && logTransInfo.Status == uint32(payLogDao.StatusWaitPay) {
			logUpdate, updateUser, userId, productId = true, true, logTransInfo.UserID, logTransInfo.ProductID
		} else if logTransInfo.SubType != "" && logTransInfo.SubType != lastPaymentTime {
			updateUser, userId, productId = true, logTransInfo.UserID, logTransInfo.ProductID
		} else {
			updateUser, logStatus = false, uint32(payLogDao.StatusIgnore)
		}
	default:
		updateUser, logStatus = false, uint32(payLogDao.StatusIgnore)
	}

	var (
		expireTime, gracePeriod int64

		uInfo   = &user.Model{}
		vipBill = &vipBillDao.Model{}
	)
	if userId != dbs.False {
		if uInfo, err = e.UserSrv.GetUserInfo(ctx, userId); err != nil {
			return err
		}

		if expireTime, gracePeriod, err = e.getProductExpireTime(ctx, uInfo.VipTime, productId); err != nil {
			return err
		}

		vipBill = &vipBillDao.Model{
			UserID:     uInfo.ID,
			Platform:   uint32(common.VipPlatPayPal),
			LogID:      logTransInfo.ID,
			Amount:     amount,
			Currency:   currency,
			Env:        env,
			BillType:   vipBillDao.BtPayPalPay,
			Date:       date,
			BeforeTime: uInfo.VipTime,
			AfterTime:  expireTime,
			ChannelID:  uInfo.ChannelID,
		}
	}

	// 业务逻辑处理
	payLog := &payLog.Model{
		UserID:                uInfo.ID,
		Platform:              uint32(common.VipPlatApple),
		LogType:               uint32(payLogDao.LogNotify),
		Status:                logStatus,
		ProductID:             productId,
		TransactionID:         tranceId,
		OriginalTransactionID: tranceId,
		PurchaseToken:         "",
		Amount:                amount,
		Currency:              currency,
		NotifyID:              event.Id,
		NotifyType:            event.EventType,
		SubType:               lastPaymentTime,
		ExpiresTime:           expireTime,
		Env:                   env,
		ChannelID:             uInfo.ChannelID,
		TraceID:               helper.GetGinRequestID(ctx),
		Func:                  "PaypalNotifyDecode",
		Date:                  date,
		Header:                string(headers),
		ReqData:               string(reqData),
		RespData:              string(event.Resource),
	}

	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	var logId uint64
	if err := func() (err error) {
		if logUpdate {
			updateData := map[string]interface{}{
				"status":       payLogDao.StatusPaid,
				"amount":       amount,
				"currency":     currency,
				"expires_time": expireTime,
			}
			if lastPaymentTime != "" {
				updateData["sub_type"] = lastPaymentTime
			}
			if err = e.PayLogRepo.UpdateMapByIDWithTx(ctx, tx, logTransInfo.ID, updateData); err != nil {
				return
			}
			logId = logTransInfo.ID
		} else {
			if err = e.PayLogRepo.CreateWithTx(ctx, tx, payLog); err != nil {
				return
			}
			logId = payLog.ID
		}

		if updateUser {
			vipBill.LogID = logId
			if err = e.VipBillRepo.CreateWithTx(ctx, tx, vipBill); err != nil {
				return
			}

			if err = e.UserRepo.UpdateMapByIDWithTx(ctx, tx, uInfo.ID, map[string]interface{}{
				"vip_time":     expireTime,
				"grace_period": gracePeriod,
			}); err != nil {
				return
			}
		}

		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("AppStoreNotify err")
		return err
	}

	return nil
}
