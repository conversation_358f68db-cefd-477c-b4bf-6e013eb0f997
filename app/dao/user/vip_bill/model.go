package vipBill

import (
	"time"

	"vlab/app/common/dbs"
)

type BillType uint32

var (
	BtAppStorePay BillType = 1 // app store pay
	BtGooglePay   BillType = 2 // google pay
	BtPayPalPay   BillType = 3 // paypal pay
	BtStripePay   BillType = 4 // stripe pay
)

type Env string

var (
	EnvSandBox    = "sandbox"    // 沙箱环境
	EnvProduction = "production" // 生产环境
)

// TODO 分表 or kafka异步写入es
type Model struct {
	ID         uint64    `gorm:"primarykey" json:"id"`
	UserID     uint64    `json:"user_id,omitempty"`
	Platform   uint32    `json:"platform,omitempty"`
	LogID      uint64    `json:"log_id,omitempty"`
	Amount     int64     `json:"amount,omitempty"`
	Currency   string    `json:"currency,omitempty"`
	Product    string    `json:"product,omitempty"`
	Env        string    `json:"env,omitempty"`
	BillType   BillType  `json:"bill_type,omitempty"`
	Date       string    `json:"date,omitempty"`
	BeforeTime int64     `json:"before_time,omitempty"`
	AfterTime  int64     `json:"after_time,omitempty"`
	ChannelID  uint64    `json:"channel_id,omitempty"`
	IsDeleted  uint32    `json:"is_deleted,omitempty"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "user_vip_bill"
}

type Filter struct {
	ID   uint64
	Sort dbs.CommonSort
}

type ModelList []*Model

type Product struct {
	ID                   uint   `gorm:"primaryKey NOT NULL AUTO_INCREMENT"`
	ProductId            string `json:"product_id" gorm:"index:product_id_app_id_deleted_at;type:varchar(64) DEFAULT ''"`
	ProductType          int    `json:"product_type" gorm:"comment:商品类型,0.未知类型/1.消耗型购买/2.非消耗型购买/3.自动订阅/4.非自动订阅;type:tinyint(4) DEFAULT 0"`
	Name                 string `json:"name" gorm:"comment:商品名称;type:varchar(64) DEFAULT ''"`
	Description          string `json:"description" gorm:"comment:商品描述;type:varchar(1024) DEFAULT ''"`
	Price                int    `json:"price" gorm:"comment:商品价格,精度2位小数,用100倍存储;type:bigint DEFAULT 0"`
	TokenType            int    `json:"token_type" gorm:"comment:虚拟币类型,0.钻石;1.金币;2.元宝;3.其它;type:tinyint(4) DEFAULT 0"`
	TokenQuantity        int    `json:"token_quantity" gorm:"comment:虚拟币数量;type:int(11) DEFAULT 0"`
	SubscribeDurationDay int    `json:"subscribe_duration_day" gorm:"comment:会员订阅时长(天);type:int(11) DEFAULT 0"`
	Weight               int    `json:"weight" gorm:"comment:权重，排序时从大到小，客户端根据此字段进行商品排序;type:int(11) DEFAULT 0"`
	ImageURL             string `json:"image_url" gorm:"comment:商品图片URL;type:varchar(1024) DEFAULT ''"`
	IsAutoSubscribe      int    `json:"is_auto_subscribe" gorm:"comment:是否是自动续费,0.未知/1.自动续费/2.非自动续费;type:tinyint(2) DEFAULT 0"`
	Platform             int    `json:"platform" gorm:"comment:支付平台, 0-海外,1-微信支付,2-国内支付渠道;type:tinyint(2) DEFAULT 0"`
}
