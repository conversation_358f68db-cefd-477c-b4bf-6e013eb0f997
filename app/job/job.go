package job

import (
	"context"
	"sync"
	"vlab/app/job/video/bytes"
	"vlab/pkg/log"

	"github.com/robfig/cron/v3"
)

var (
	jobInit     sync.Once
	jobInstance *Job
)

type Job struct {
	cron    *cron.Cron
	mu      sync.Mutex
	entries map[string]cron.EntryID
}

func GetJob() *Job {
	if jobInstance == nil {
		jobInit.Do(func() {
			jobInstance = &Job{
				cron:    cron.New(cron.WithSeconds()), // 支持秒级任务
				entries: make(map[string]cron.EntryID),
			}
		})
	}
	return jobInstance
}

// AddTask 添加任务
func (j *Job) AddTask(ctx context.Context, name string, schedule string, task func()) error {
	j.mu.Lock()
	defer j.mu.Unlock()

	if _, exists := j.entries[name]; exists {
		return nil
	}
	log.WithContext(ctx).WithField("name", name).With<PERSON>ield("schedule", schedule).Info("Job AddTask")

	id, err := j.cron.AddFunc(schedule, task)
	if err != nil {
		log.WithContext(ctx).WithError(err).Warn("AddTask err")
		return err
	}

	j.entries[name] = id
	return nil
}

// RemoveTask 删除任务
func (j *Job) RemoveTask(ctx context.Context, name string) {
	j.mu.Lock()
	defer j.mu.Unlock()

	id, exists := j.entries[name]
	if !exists {
		log.WithContext(ctx).WithField("name", name).Info("Task not exists")
		return
	}

	j.cron.Remove(id)
	delete(j.entries, name)
}

// Start 启动定时任务
func (j *Job) Start(ctx context.Context) error {
	j.InitTasks(ctx)
	j.cron.Start()
	return nil
}

// Close 停止定时任务
func (j *Job) Close(ctx context.Context) error {
	j.cron.Stop()
	return nil
}

func (j *Job) Name() string {
	return "Job"
}

func (j *Job) InitTasks(ctx context.Context) {
	j.AddTask(ctx, "BytesVideoTask", "0 */1 * * * *", func() {
		// 每5分钟查询一次点播url拉取任务结果
		if err := bytes.GetJob().UpdateVodPullJobStatus(); err != nil {
			log.WithContext(ctx).WithError(err).Warn("UpdateVodPullJobStatus error")
		}
	})

	// j.AddTask(ctx, "BytesVideoTaskCp", "0 */1 * * * *", func() {
	// 	// 每分钟查询一次点播url拉取任务结果 - cp表
	// 	if err := bytes.GetJob().UpdateVodPullJobStatusCp(); err != nil {
	// 		log.WithContext(ctx).WithError(err).Warn("UpdateVodPullJobStatusCp error")
	// 	}

	// 	// 每分钟将cp表中数据同步会mps表
	// 	if err := bytes.GetJob().SyncMpsCpSuccessToMps(); err != nil {
	// 		log.WithContext(ctx).WithError(err).Warn("SyncMpsCpSuccessToMps error")
	// 	}
	// })

	// 20250527 迁移至字节，不再支持阿里云转码功能，后续待定
	// j.AddTask(ctx, "VideoTask", "0 */5 * * * *", func() {
	// 	// 每5分钟查询一次未转码成功的任务
	// 	if err := aliyunJob.GetJob().UpdateTranscodeJobStatus(); err != nil {
	// 		log.WithContext(ctx).WithError(err).Warn("UpdateTranscodeJobStatus error")
	// 	}
	// 	// 每5分钟查询一次点播url拉取任务结果
	// 	if err := aliyunJob.GetJob().UpdateVodPullJobStatus(); err != nil {
	// 		log.WithContext(ctx).WithError(err).Warn("UpdateVodPullJobStatus error")
	// 	}

	// 	// 每5分钟查询点播本地上传视频结果
	// 	if err := aliyunJob.GetJob().UpdateVodUploadJobStatus(); err != nil {
	// 		log.WithContext(ctx).WithError(err).Warn("UpdateVodUploadJobStatus error")
	// 	}
	// })

	//if config.AppCfg != nil && config.AppCfg.Env == "prod" {
	//	j.AddTask(ctx, "LokTask", "0 0 */8 * * *", func() {
	//		// 每8小时执行一次
	//
	//		if err := lok.GetJob().ScheduleLokReq(); err != nil {
	//			log.WithContext(ctx).WithError(err).Warn("ScheduleLokReq error")
	//		}
	//
	//	})
	//}
}
