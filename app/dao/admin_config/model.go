package adminConfig

import (
	"time"
	"vlab/app/common/dbs"
)

type ConfigKey string

const (
	KeyCurrentEnv                      ConfigKey = "currentEnv"                       // 当前环境
	KeyEnableReplayProtection          ConfigKey = "enable_replay_protection"         // 防重放攻击开关
	KeyReplayProtectionTtl             ConfigKey = "replay_protection_ttl"            // 防重放攻击ttl
	KeyVideoSignUrlTtl                 ConfigKey = "video_sign_url_ttl"               // 视频播放url加签时间(单位：分钟)
	KeyVideoTranscodeFormat            ConfigKey = "video_transcode_format"           // 视频转码格式 1m3u8 2mp4
	KeyPassCheck                       ConfigKey = "pass_check"                       // 通行证
	KeyVodTranscodeTemplateGroupId     ConfigKey = "vod_transcode_temp_group_id"      // 视频点播转码模板组id
	KeyVodPullTranscodeTemplateGroupId ConfigKey = "vod_pull_transcode_temp_group_id" // 视频点播拉取转码模板组id
	KeyUserWatchAdNumDay               ConfigKey = "user_watch_ad_num_day"            // 用户每天观看广告次数限制
	KeyUserWatchAdAddTime              ConfigKey = "user_watch_ad_add_time"           // 用户观看广告时间增加时长(单位：秒)
	KeyVisitorWatchAdAddTime           ConfigKey = "visitor_watch_ad_add_time"        // 游客观看广告时间增加时长(单位：秒)
	KeyNewUserNoAdTime                 ConfigKey = "new_user_no_ad_time"              // 新用户免广告时长(单位：秒)
	KeyBytesVodStartVideoID            ConfigKey = "bytesVodStartVideoID"             // 字节视频点播开始video id
	KeyEnableShowSearchVector          ConfigKey = "enable_show_search_vector"        // 节目搜索接口是否启用向量搜索
	KeyEnableAdminShowListVector       ConfigKey = "enable_admin_show_list_vector"    // 管理后台节目列表接口是否启用向量搜索
)

type Model struct {
	ID          uint64    `json:"id,omitempty"`
	ConfigKey   string    `json:"config_key,omitempty"`
	ConfigValue string    `json:"config_value,omitempty"`
	Desc        string    `json:"desc,omitempty"`
	Status      uint32    `json:"status,omitempty"`
	IsDeleted   uint32    `json:"is_deleted,omitempty"` // 软删
	CreatedAt   time.Time `json:"created_at"`           // 创建时间
	UpdatedAt   time.Time `json:"updated_at"`           // 修改时间
}

func (m *Model) TableName() string {
	return "admin_config"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type Filter struct {
	ID     uint64
	IDS    []uint64
	Status uint32
	Sort   dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetConfigMap() map[string]string {
	retMap := make(map[string]string, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ConfigKey]; !ok {
			retMap[val.ConfigKey] = val.ConfigValue
		}
	}
	return retMap
}

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

func (ml ModelList) GetIDEmptyMap() map[uint64]struct{} {
	retMap := make(map[uint64]struct{}, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = struct{}{}
		}
	}
	return retMap
}
