package vip

import (
	productDao "vlab/app/dao/vip/product"
	"vlab/pkg/ecode"
	"vlab/pkg/log"
	"vlab/pkg/util/timeUtil"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

func (e *Entry) getProductExpireTime(ctx *gin.Context, oglTime int64, productID string) (int64, int64, error) {
	var (
		expireTime, gracePeriod int64
		basicTime               = carbon.Now(timeUtil.GetCurrentZone(ctx)).Timestamp()
		renewTime               = basicTime
		gracePeriadDay          = productDao.ProductIDMap[productID]
	)
	if oglTime > basicTime {
		basicTime = oglTime
	}
	if oglTime != 0 {
		renewTime = oglTime
	}

	switch productID {
	case string(productDao.ProductSingleMonth):
		expireTime = carbon.CreateFromTimestamp(basicTime, timeUtil.GetCurrentZone(ctx)).AddMonth().Timestamp()
	case string(productDao.ProductSingleQuarter):
		expireTime = carbon.CreateFromTimestamp(basicTime, timeUtil.GetCurrentZone(ctx)).AddQuarter().Timestamp()
	case string(productDao.ProductSingleYear):
		expireTime = carbon.CreateFromTimestamp(basicTime, timeUtil.GetCurrentZone(ctx)).AddYear().Timestamp()

	case string(productDao.ProductMonthly):
		expireTime = carbon.CreateFromTimestamp(renewTime, timeUtil.GetCurrentZone(ctx)).AddMonth().Timestamp()
		gracePeriod = expireTime + int64(gracePeriadDay*24*3600)
	case string(productDao.ProductQuarterly):
		expireTime = carbon.CreateFromTimestamp(renewTime, timeUtil.GetCurrentZone(ctx)).AddQuarter().Timestamp()
		gracePeriod = expireTime + int64(gracePeriadDay*24*3600)
	case string(productDao.ProductYearly):
		expireTime = carbon.CreateFromTimestamp(renewTime, timeUtil.GetCurrentZone(ctx)).AddYear().Timestamp()
		gracePeriod = expireTime + int64(gracePeriadDay*24*3600)
	default:
		log.Ctx(ctx).WithField("productID", productID).Error("productIDNotFound")
		return expireTime, gracePeriod, ecode.ParamInvalidErr
	}

	return expireTime, gracePeriod, nil
}

func (e *Entry) getStripeProductExpireTime(ctx *gin.Context, oglTime int64, productID string) (int64, int64, error) {
	var (
		expireTime, gracePeriod int64
		basicTime               = carbon.Now(timeUtil.GetCurrentZone(ctx)).Timestamp()
		renewTime               = basicTime
		gracePeriadDay          = productDao.ProductIDMap[productID]
	)
	if oglTime > basicTime {
		basicTime = oglTime
	}
	if oglTime != 0 {
		renewTime = oglTime
	}

	switch productID {
	case string(productDao.ProductSingleMonth):
		expireTime = carbon.CreateFromTimestamp(basicTime, timeUtil.GetCurrentZone(ctx)).AddMonth().Timestamp()
	case string(productDao.ProductSingleQuarter):
		expireTime = carbon.CreateFromTimestamp(basicTime, timeUtil.GetCurrentZone(ctx)).AddQuarter().Timestamp()
	case string(productDao.ProductSingleYear):
		expireTime = carbon.CreateFromTimestamp(basicTime, timeUtil.GetCurrentZone(ctx)).AddYear().Timestamp()

	case string(productDao.ProductMonthly):
		expireTime = carbon.CreateFromTimestamp(renewTime, timeUtil.GetCurrentZone(ctx)).AddMonth().Timestamp()
		gracePeriod = expireTime + int64(gracePeriadDay*24*3600)
	case string(productDao.ProductQuarterly):
		expireTime = carbon.CreateFromTimestamp(renewTime, timeUtil.GetCurrentZone(ctx)).AddQuarter().Timestamp()
		gracePeriod = expireTime + int64(gracePeriadDay*24*3600)
	case string(productDao.ProductYearly):
		expireTime = carbon.CreateFromTimestamp(renewTime, timeUtil.GetCurrentZone(ctx)).AddYear().Timestamp()
		gracePeriod = expireTime + int64(gracePeriadDay*24*3600)
	default:
		log.Ctx(ctx).WithField("productID", productID).Error("productIDNotFound")
		return expireTime, gracePeriod, ecode.ParamInvalidErr
	}

	return expireTime, gracePeriod, nil
}
