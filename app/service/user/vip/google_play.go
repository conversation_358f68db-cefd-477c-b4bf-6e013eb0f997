package vip

import (
	"fmt"
	googlePlayExec "vlab/app/api/googleplay/execute"
	"vlab/app/common/dbs"
	adminConfig "vlab/app/dao/admin_config"
	"vlab/app/dao/common"
	"vlab/app/dao/user"
	vipBill "vlab/app/dao/user/vip_bill"
	payLog "vlab/app/dao/vip/pay_log"
	productDao "vlab/app/dao/vip/product"
	userDto "vlab/app/dto/user"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"
	"vlab/pkg/redis"
	"vlab/pkg/util/timeUtil"

	"github.com/gin-gonic/gin"
	"google.golang.org/api/androidpublisher/v3"
)

type (
	GooglePlayTransactionRet struct {
		ProductPurchase *androidpublisher.ProductPurchase        `json:"productPurchase"`
		SubPurchase     *androidpublisher.SubscriptionPurchaseV2 `json:"subPurchase"`
		OrderInfo       *androidpublisher.Order                  `json:"orderInfo"`
	}
)

// GooglePlayTransaction 处理Google Play支付事务
func (e *Entry) GooglePlayTransaction(ctx *gin.Context, UID uint64, req userDto.GooglePlayTransactionReq) (*userDto.AdminUserListItem, error) {
	if err := e.RedisCli.Lock(ctx, redis.GetUserGooglePlayTrance(UID, req.ProductId), redis.LockTimeHalfMinute); err != nil {
		return nil, ecode.UserGooglePlayTraceIDUsedErr
	}
	defer e.RedisCli.Unlock(ctx, redis.GetUserGooglePlayTrance(UID, req.ProductId))
	log.Ctx(ctx).WithField("uid", UID).WithField("req", req).Info("GooglePlayTransactionStart")

	var (
		commonProductId = req.ProductId
		ok              bool
		channelID, _    = helper.GetCtxChannelID(ctx)
	)
	if channelID == uint64(common.ChannelViBoxAndroid) || channelID == uint64(common.ChannelHitvBoxAndroid) {
		if commonProductId, ok = productDao.ToCommonProductMap[productDao.MapProduct(commonProductId)]; !ok {
			return nil, ecode.UserConfigVIpProductNotExistErr
		}
	}

	var (
		orderId         string
		isSub           = productDao.IsSub(commonProductId)
		err             error
		productPurchase = &androidpublisher.ProductPurchase{}
		subPurchase     = &androidpublisher.SubscriptionPurchaseV2{}
		appEnv          = adminConfig.RedisGetConfig[string](ctx, adminConfig.KeyCurrentEnv)
	)
	if isSub {
		if subPurchase, err = e.GooglePlayExec.PurchaseSubscriptionInfo(ctx, req.PackageName, req.PurchaseToken); err != nil {
			log.Ctx(ctx).WithError(err).Error("e.GooglePlayExec.PurchaseSubscriptionInfo error")
			return nil, err
		}
		orderId = subPurchase.LineItems[0].LatestSuccessfulOrderId
	} else {
		if productPurchase, err = e.GooglePlayExec.PurchaseProductInfo(ctx, req.PackageName, req.ProductId, req.PurchaseToken); err != nil {
			log.Ctx(ctx).WithError(err).Error("e.GooglePlayExec.PurchaseProductInfo error")
			return nil, err
		}
		if productPurchase.PurchaseState != 0 {
			return nil, ecode.UserGooglePlayPurchaseStateErr
		}
		orderId = productPurchase.OrderId
	}

	uInfo, err := e.UserSrv.GetUserInfo(ctx, UID)
	if err != nil {
		return nil, err
	}
	logTransInfo, err := e.PayLogRepo.FeatchByPurchaseToken(ctx, req.PurchaseToken)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("e.PayLogRepo.FeatchByPurchaseToken error")
		return nil, err
	}
	if logTransInfo.ID != dbs.False {
		return uInfo.ToUserInfoResp(ctx), nil
	}

	// TODO 异步处理
	if !(dbs.IsTest(appEnv) && req.NotAck == dbs.True) {
		if isSub {
			if err = e.GooglePlayExec.PurchaseSubscriptionAcknowledge(ctx, req.PackageName, req.ProductId, req.PurchaseToken); err != nil {
				log.Ctx(ctx).WithError(err).WithField("subPurchase", subPurchase).Error("e.GooglePlayExec.PurchaseSubscriptionAcknowledge error")
			}
		} else {
			if err = e.GooglePlayExec.PurchasedProductAcknowledge(ctx, req.PackageName, req.ProductId, req.PurchaseToken); err != nil {
				log.Ctx(ctx).WithError(err).WithField("productPurchase", productPurchase).Error("e.GooglePlayExec.PurchasedProductAcknowledge error")
			}

			if err = e.GooglePlayExec.PurchasedProductConsume(ctx, req.PackageName, req.ProductId, req.PurchaseToken); err != nil {
				log.Ctx(ctx).WithError(err).WithField("productPurchase", productPurchase).Error("e.GooglePlayExec.PurchasedProductConsume error")
			}
		}
	}

	orderInfo, err := e.GooglePlayExec.OrderInfo(ctx, req.PackageName, orderId)
	if err != nil {
		log.Ctx(ctx).WithError(err).WithField("orderId", orderId).Error("e.GooglePlayExec.OrderInfo error")
		return nil, err
	}
	if orderInfo.State != "PROCESSED" {
		return nil, ecode.UserGooglePlayOrderStateErr
	}
	amount := orderInfo.Total.Units*********** + orderInfo.Total.Nanos

	// 业务逻辑处理
	var (
		decodeResp = &GooglePlayTransactionRet{
			ProductPurchase: productPurchase,
			SubPurchase:     subPurchase,
			OrderInfo:       orderInfo,
		}
		headers, _  = json.Marshal(ctx.Request.Header)
		reqData, _  = json.Marshal(req)
		respData, _ = json.Marshal(decodeResp)
		date        = timeUtil.NowToDateStringByZone(ctx)
		env         = e.AppStoreExec.GetCurrentEnv(ctx)
	)

	expireTime, gracePeriod, err := e.getProductExpireTime(ctx, uInfo.VipTime, commonProductId)
	if err != nil {
		return nil, err
	}

	payLog := &payLog.Model{
		UserID:                uInfo.ID,
		Platform:              uint32(common.VipPlatGoogle),
		LogType:               uint32(payLog.LogRequest),
		Status:                uint32(payLog.StatusPaid),
		ProductID:             commonProductId,
		TransactionID:         orderId,
		OriginalTransactionID: orderId,
		PurchaseToken:         req.PurchaseToken,
		Amount:                amount,
		Currency:              orderInfo.Total.CurrencyCode,
		NotifyID:              "",
		NotifyType:            "client",
		SubType:               "client",
		ExpiresTime:           expireTime,
		Env:                   env,
		ChannelID:             channelID,
		TraceID:               helper.GetGinRequestID(ctx),
		Func:                  "VerifyPurchase",
		Date:                  date,
		Header:                string(headers),
		ReqData:               string(reqData),
		RespData:              string(respData),
	}
	vipBill := &vipBill.Model{
		UserID:     uInfo.ID,
		Platform:   uint32(common.VipPlatGoogle),
		Amount:     amount,
		Currency:   orderInfo.Total.CurrencyCode,
		Product:    commonProductId,
		Env:        env,
		BillType:   vipBill.BtGooglePay,
		Date:       date,
		BeforeTime: uInfo.VipTime,
		AfterTime:  expireTime,
		ChannelID:  channelID,
	}

	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	if err := func() (err error) {
		if err = e.PayLogRepo.CreateWithTx(ctx, tx, payLog); err != nil {
			return
		}
		vipBill.LogID = payLog.ID

		if err = e.VipBillRepo.CreateWithTx(ctx, tx, vipBill); err != nil {
			return
		}
		updateData := map[string]interface{}{
			"vip_time":     expireTime,
			"grace_period": gracePeriod,
		}

		if err = e.UserRepo.UpdateMapByIDWithTx(ctx, tx, uInfo.ID, updateData); err != nil {
			return
		}
		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("GooglePlayTransaction err")
		return nil, err
	}

	uInfo.VipTime = expireTime
	return uInfo.ToUserInfoResp(ctx), nil
}

// AppStoreNotify .
func (e *Entry) GooglePlayNotify(ctx *gin.Context, notifyId string, notifyData *googlePlayExec.DeveloperNotification) error {
	var (
		purchaseToken, productId string
		isSub, ok                bool
		orderId, uuid            string
		err                      error
		productPurchase          = &androidpublisher.ProductPurchase{}
		subPurchase              = &androidpublisher.SubscriptionPurchaseV2{}
		channelID, _             = helper.GetCtxChannelID(ctx)
		commonProductId          string
	)
	log.Ctx(ctx).WithField("notifyId", notifyId).WithField("notifyData", notifyData).Debug("GooglePlayNotifyData")

	if notifyData.SubscriptionNotification != nil {
		isSub, purchaseToken = true, notifyData.SubscriptionNotification.PurchaseToken
	} else if notifyData.OneTimeProductNotification != nil {
		isSub, purchaseToken = false, notifyData.OneTimeProductNotification.PurchaseToken
		productId = notifyData.OneTimeProductNotification.SKU
	} else if notifyData.TestNotification != nil || notifyData.VoidedPurchaseNotification != nil {
		return nil
	} else {
		return nil
	}

	if purchaseToken == "" {
		log.Ctx(ctx).Info("GooglePlayNotify purchaseToken is empty")
		return ecode.ParamInvalidErr
	}

	if isSub {
		if subPurchase, err = e.GooglePlayExec.PurchaseSubscriptionInfo(ctx, notifyData.PackageName, purchaseToken); err != nil {
			log.Ctx(ctx).WithError(err).Error("GooglePlayExec.PurchaseSubscriptionInfo error")
			return err
		}
		// TODO 确认是否取第一个
		orderId, productId, uuid = subPurchase.LineItems[0].LatestSuccessfulOrderId, subPurchase.LineItems[0].ProductId, subPurchase.ExternalAccountIdentifiers.ObfuscatedExternalAccountId
	} else {
		if productPurchase, err = e.GooglePlayExec.PurchaseProductInfo(ctx, notifyData.PackageName, productId, purchaseToken); err != nil {
			log.Ctx(ctx).WithError(err).Error("GooglePlayExec.PurchaseProductInfo error")
			return err
		}
		orderId, uuid = productPurchase.OrderId, productPurchase.ObfuscatedExternalAccountId
	}
	if channelID == uint64(common.ChannelViBoxAndroid) || channelID == uint64(common.ChannelHitvBoxAndroid) {
		if commonProductId, ok = productDao.ToCommonProductMap[productDao.MapProduct(productId)]; !ok {
			return ecode.UserConfigVIpProductNotExistErr
		}
	}
	orderInfo, err := e.GooglePlayExec.OrderInfo(ctx, notifyData.PackageName, orderId)
	if err != nil {
		log.Ctx(ctx).WithError(err).WithField("orderId", orderId).Error("e.GooglePlayExec.OrderInfo error")
		return err
	}
	if orderInfo.State != "PROCESSED" {
		log.Ctx(ctx).WithField("orderInfo", orderInfo).Info("GooglePlayNotify orderState is not PROCESSED")
		return nil
	}
	amount := orderInfo.Total.Units*********** + orderInfo.Total.Nanos

	var (
		decodeResp = &GooglePlayTransactionRet{
			ProductPurchase: productPurchase,
			SubPurchase:     subPurchase,
			OrderInfo:       orderInfo,
		}
		headers, _       = json.Marshal(ctx.Request.Header)
		reqData, _       = json.Marshal(notifyData)
		respData, _      = json.Marshal(decodeResp)
		date             = timeUtil.NowToDateStringByZone(ctx)
		uInfo            = &user.Model{}
		updateUser  bool = false
		logStatus        = uint32(payLog.StatusIgnore)
		env              = e.AppStoreExec.GetCurrentEnv(ctx)
		notifyType  string
		subType     string
	)

	if uInfo, err = e.UserRepo.FetchByUUID(ctx, uuid); err != nil {
		log.Ctx(ctx).WithError(err).Error("e.UserRepo.FetchByUUID error")
		return err
	}
	if uInfo.ID == 0 {
		log.Ctx(ctx).WithField("uuid", uuid).Error("UUIDNotFound")
		return ecode.ParamInvalidErr
	}

	logTransInfo, err := e.PayLogRepo.FeatchByPurchaseToken(ctx, purchaseToken)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("e.PayLogRepo.FeatchByPurchaseToken error")
		return err
	}

	expireTime, gracePeriod, err := e.getProductExpireTime(ctx, uInfo.VipTime, commonProductId)
	if err != nil {
		return err
	}

	if isSub {
		switch notifyData.SubscriptionNotification.NotificationType {
		case googlePlayExec.SubNtfTypeRenewed, googlePlayExec.SubNtfTypePurchased:
			if logTransInfo.ID == dbs.False {
				updateUser, logStatus = true, uint32(payLog.StatusPaid)
				if err = e.GooglePlayExec.PurchaseSubscriptionAcknowledge(ctx, notifyData.PackageName, productId, purchaseToken); err != nil {
					log.Ctx(ctx).WithError(err).WithField("subPurchase", subPurchase).Error("GooglePlayExec.PurchaseSubscriptionAcknowledge error")
				}
			}
		default:
			status := payLog.StatusIgnore
			if logTransInfo.ID != dbs.False {
				logStatus = uint32(payLog.StatusRepeat)
			}
			log.Ctx(ctx).WithField("status", status).Info("GooglePlayNotify Status")
		}
		notifyType, subType = "sub", fmt.Sprintf("%s-%v", "sub", notifyData.SubscriptionNotification.NotificationType)
	} else {
		switch notifyData.OneTimeProductNotification.NotificationType {
		case googlePlayExec.OneNtfTypePurchased:
			if logTransInfo.ID == dbs.False {
				updateUser, logStatus = true, uint32(payLog.StatusPaid)
				if err = e.GooglePlayExec.PurchasedProductAcknowledge(ctx, notifyData.PackageName, productId, purchaseToken); err != nil {
					log.Ctx(ctx).WithError(err).WithField("productPurchase", productPurchase).Error("GooglePlayExec.PurchasedProductAcknowledge error")
				}

				if err = e.GooglePlayExec.PurchasedProductConsume(ctx, notifyData.PackageName, productId, purchaseToken); err != nil {
					log.Ctx(ctx).WithError(err).WithField("productPurchase", productPurchase).Error("GooglePlayExec.PurchasedProductConsume error")
				}
			}
		case googlePlayExec.OneNtfTypeCanceled:
			updateUser, logStatus = false, uint32(payLog.StatusIgnore)
		}
		notifyType, subType = "onetime", fmt.Sprintf("%s-%v", "onetime", notifyData.OneTimeProductNotification.NotificationType)
	}

	// 业务逻辑处理
	payLog := &payLog.Model{
		UserID:                uInfo.ID,
		Platform:              uint32(common.VipPlatGoogle),
		LogType:               uint32(payLog.LogNotify),
		Status:                logStatus,
		ProductID:             commonProductId,
		TransactionID:         orderId,
		OriginalTransactionID: orderId,
		PurchaseToken:         purchaseToken,
		Amount:                amount,
		Currency:              orderInfo.Total.CurrencyCode,
		NotifyID:              notifyId,
		NotifyType:            notifyType,
		SubType:               subType,
		ExpiresTime:           expireTime,
		Env:                   env,
		ChannelID:             uInfo.ChannelID,
		TraceID:               helper.GetGinRequestID(ctx),
		Func:                  "OrderInfo",
		Date:                  date,
		Header:                string(headers),
		ReqData:               string(reqData),
		RespData:              string(respData),
	}
	vipBill := &vipBill.Model{
		UserID:     uInfo.ID,
		Platform:   uint32(common.VipPlatGoogle),
		Amount:     amount,
		Currency:   orderInfo.Total.CurrencyCode,
		Product:    commonProductId,
		Env:        env,
		BillType:   vipBill.BtGooglePay,
		Date:       date,
		BeforeTime: uInfo.VipTime,
		AfterTime:  expireTime,
		ChannelID:  uInfo.ChannelID,
	}

	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	if err := func() (err error) {
		if err = e.PayLogRepo.CreateWithTx(ctx, tx, payLog); err != nil {
			return
		}
		vipBill.LogID = payLog.ID

		if updateUser {
			if err = e.VipBillRepo.CreateWithTx(ctx, tx, vipBill); err != nil {
				return
			}

			if err = e.UserRepo.UpdateMapByIDWithTx(ctx, tx, uInfo.ID, map[string]interface{}{
				"vip_time":     expireTime,
				"grace_period": gracePeriod,
			}); err != nil {
				return
			}
		}

		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("GooglePlayNotify err")
		return err
	}

	return nil
}
