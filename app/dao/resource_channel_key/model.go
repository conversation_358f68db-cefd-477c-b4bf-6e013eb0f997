package channelkey

import (
	"time"

	"vlab/app/common/dbs"
)

type Model struct {
	ID                   uint64 `gorm:"primarykey" json:"id"`
	ChannelID            uint64 `json:"channel_id,omitempty"`
	SignKey              string `json:"sign_key,omitempty"`
	Req<PERSON><PERSON>               string `json:"req_key,omitempty"`
	Resp<PERSON><PERSON>              string `json:"resp_key,omitempty"`
	IV                   string `json:"iv,omitempty"`
	ProductUrl           string `json:"product_url,omitempty"`
	AppStoreKeyID        string `json:"app_store_key_id,omitempty"`
	AppStoreBundleID     string `json:"app_store_bundle_id,omitempty"`
	AppStoreIssuer       string `json:"app_store_issuer,omitempty"`
	AppStorePkFilename   string `json:"app_store_pk_filename,omitempty"`
	GoogleLoginClientID  string `json:"google_login_client_id,omitempty"`
	GooglePlayPkFilename string `json:"google_play_pk_filename,omitempty"`

	OssImageHost    string `json:"oss_image_host,omitempty"`    // OSS文件存储域名
	OssSubtitleHost string `json:"oss_subtitle_host,omitempty"` // OSS字幕存储域名

	Sort      uint32    `json:"sort,omitempty"`
	Status    uint32    `json:"status,omitempty"`
	IsDeleted uint32    `json:"is_deleted,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "resource_channel_key"
}

func (m *Model) CheckAppStoreConfig() bool {
	return m.AppStoreKeyID != "" && m.AppStoreBundleID != "" && m.AppStoreIssuer != "" && m.AppStorePkFilename != ""
}

func (m *Model) CheckGoogleLoginConfig() bool {
	return m.GoogleLoginClientID != ""
}

func (m *Model) CheckGooglePlayConfig() bool {
	return m.GooglePlayPkFilename != ""
}

func (m *Model) CheckOssImageHost() bool {
	return m.OssImageHost != ""
}

func (m *Model) CheckOssSubtitleHost() bool {
	return m.OssSubtitleHost != ""
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type Filter struct {
	ID        uint64
	NotID     uint64
	IDS       []uint64
	ChannelID uint64
	Status    uint32
	Sort      dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetChannelIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ChannelID]; !ok {
			retMap[val.ChannelID] = val
		}
	}
	return retMap
}

// func (ml ModelList) GetNameMap() map[string]*Model {
// 	retMap := make(map[string]*Model, len(ml))
// 	for _, val := range ml {
// 		if _, ok := retMap[val.Name]; !ok {
// 			retMap[val.Name] = val
// 		}
// 	}
// 	return retMap
// }
