package show

import (
	"vlab/app/api/aliyun/common"
	"vlab/app/common/dbs"
	episodeDao "vlab/app/dao/content_episode"
	imageDao "vlab/app/dao/content_image"
	showDao "vlab/app/dao/content_show"
	subtitleDao "vlab/app/dao/content_subtitle"
	videoDao "vlab/app/dao/content_video"
	channelkey "vlab/app/dao/resource_channel_key"
	showDto "vlab/app/dto/show"
	"vlab/app/service/resource"
	"vlab/app/service/user"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"

	"github.com/sirupsen/logrus"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm/clause"
)

func (e Entry) EpisodeDetail(ctx *gin.Context, req *showDto.EpisodeDetailReq) (resp *showDto.EpisodeDetailResp, err error) {
	resp = &showDto.EpisodeDetailResp{}

	var (
		show            = &showDao.Show{}
		showWithChannel = make(showDao.ShowWithLimitList, 0)

		episode   = &episodeDao.Episode{}
		videos    = make(videoDao.VideoList, 0)
		subtitles = make(subtitleDao.SubtitleList, 0)

		imageIDs  = make([]uint64, 0)
		images    = make(imageDao.ImageList, 0)
		episodeID uint64
	)

	channelKeyInfo, _ := helper.GetCtxChannelKeyInfo(ctx, false)
	if !channelKeyInfo.CheckOssSubtitleHost() {
		return nil, ecode.ChannelConfigInvalidErr
	}
	if !channelKeyInfo.CheckOssImageHost() {
		return nil, ecode.ChannelConfigInvalidErr
	}

	if req.Resolution == 0 {
		req.Resolution = uint32(common.Resolution_640)
	}

	episodeID = req.EpisodeID
	// 若只传入剧ID, 则返回该剧的第一集
	if episodeID == 0 && req.ShowID != 0 {
		episode, err = e.EpisodeRepo.FeatchByFilterSort(ctx, &episodeDao.Filter{
			ShowID: req.ShowID,
			Sort: []clause.OrderByColumn{
				{
					Column: clause.Column{
						Name: "episode_number"}, // episode_number 顺序
					Desc: false,
				},
			},
		})
		episodeID = episode.ID
	} else {
		episode, err = e.EpisodeRepo.FetchByID(ctx, episodeID)
		if err != nil {
			return nil, err
		}
	}

	if episode == nil || episode.Status != uint32(dbs.StatusEnable) {
		return nil, ecode.NotFoundErr
	}

	show, err = e.ShowRepo.FetchByID(ctx, episode.ShowID)
	if err != nil {
		return nil, err
	}
	if show.Status != uint32(dbs.StatusEnable) {
		return nil, ecode.NotFoundErr
	}

	versionID, _ := helper.GetCtxVersionID(ctx)
	req.VersionID = versionID

	verModels, err := resource.GetService().GetVersions(ctx, []uint64{
		req.VersionID,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("GetVersions error")
		err = ecode.SystemErr
		return nil, err
	}
	verMap := verModels.GetIDMap()
	verModel, ok := verMap[req.VersionID]
	if !ok {
		return nil, ecode.NotFoundErr
	}

	if t := verModel; t != nil {
		switch t.Status {
		case uint32(dbs.StatusEnable): // 若该版本已上线, 则需要正常返回, 不需要过滤
			// filter.AuditType = uint32(showDao.LimitTypeAuditStatusNone)
			// 若该版本已上线, 则不需要过滤渠道
		case uint32(dbs.StatusDisable): // 若该版本已下线, 则需要返回空
			return resp, nil
		case uint32(dbs.StatusAuditIng): // 若该版本在审核中, 则需要过滤渠道, 直接展示审核剧
			//filter.AuditType = uint32(showDao.LimitTypeAuditStatusProcessing)
			showWithChannel, err = e.ShowRepo.FindLimitByFilter(ctx, &showDao.LimitFilter{
				LimitID: req.ChannelID,
				Type:    uint32(showDao.LimitTypeChannel),
				ShowID:  episode.ShowID,
			})
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("FindLimitByFilter error")
				err = ecode.SystemErr
				return nil, err
			}
			if len(showWithChannel) == 0 {
				return nil, ecode.NotFoundErr
			}
		}
	}

	videos, err = e.VideoRepo.FindByFilter(ctx, &videoDao.Filter{
		EpisodeID: episodeID,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("EpisodeDetail error")
		err = ecode.SystemErr
		return nil, err
	}
	if len(videos) == 0 {
		return nil, ecode.NotFoundErr
	}
	imageIDs = append(imageIDs, videos.GetImageIDs()...)

	if t := videos.GetIDs(); len(t) > 0 {
		subtitles, err = e.SubtitleRepo.FindByFilter(ctx, &subtitleDao.Filter{
			VideoIDs:   videos.GetIDs(),
			EpisodeIDs: []uint64{episodeID},
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("EpisodeDetail error")
		}
	}

	if len(imageIDs) > 0 {
		images, err = e.ImageRepo.FindByFilter(ctx, &imageDao.Filter{
			IDs: imageIDs,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("EpisodeDetail error")
		}
	}

	e.setEpisodeDetailResp(ctx, resp, episode, videos, subtitles, images, channelKeyInfo)

	if !user.GetService().CheckWatchAdTime(ctx) {
		return resp, nil
	}

	url, resolution, runtime, err := GetService().GetVideoSignedUrl(ctx, videos[0], common.Resolution(req.Resolution))
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("GetVideoSignedUrl error")
		return nil, err
	}

	resp.Videos[0].VideoPath = url
	resp.Videos[0].Resolution = uint32(resolution)
	resp.Videos[0].Runtime = videos[0].Runtime // 接口时长
	resp.Videos[0].RealRuntime = runtime       // 实际时长

	if url == "" {
		// TODO: 处理无播放链接的视频
		log.Ctx(ctx).WithFields(logrus.Fields{"videoID": videos[0].ID}).Warn("GetVideoSignedUrl url is empty")
		return
	}

	// watch video log
	GetWatchVideoEntity().Producer(ctx, req.ShowID, episodeID)
	return
}

func (e Entry) setEpisodeDetailResp(
	ctx *gin.Context,
	resp *showDto.EpisodeDetailResp,
	episode *episodeDao.Episode,
	videos videoDao.VideoList,
	subtitles subtitleDao.SubtitleList,
	images imageDao.ImageList,
	channelKeyInfo *channelkey.Model,
) {
	var (
		subtitleVideoKeyMap = subtitles.GetVideoKeyMap()
		imageMap            = images.GetMap()
	)

	resp.EpisodeBase = &showDto.EpisodeBase{}
	resp.Videos = make([]*showDto.VideoBase, 0)
	resp.EpisodeBase.Id = episode.ID
	resp.Name = episode.Name
	resp.EpisodeNumber = episode.EpisodeNumber

	for _, video := range videos {
		videoBase := &showDto.VideoBase{}
		videoBase.Id = video.ID
		videoBase.Name = video.Name

		videoBase.Runtime = video.Runtime

		if image, ok := imageMap[video.ImageID]; ok {
			videoBase.Still = &showDto.ImageBase{
				AspectRatio: image.AspectRatio,
				Height:      image.Height,
				Iso6391:     image.ISO_639_1,
				FilePath:    image.GetFilePath(channelKeyInfo.OssImageHost),
				Width:       image.Width,
			}
			videoBase.Still.AspectType = image.SetAspectType()
		}

		if subtitles, ok := subtitleVideoKeyMap[video.ID]; ok {
			videoBase.Subtitles = make([]*showDto.SubtitleBase, 0)
			for _, subtitle := range subtitles {
				subtitleBase := &showDto.SubtitleBase{}
				subtitleBase.Id = subtitle.ID
				subtitleBase.Iso6391 = subtitle.ISO_639_1
				subtitleBase.FilePath = subtitle.GetFilePath(channelKeyInfo.OssSubtitleHost)
				videoBase.Subtitles = append(videoBase.Subtitles, subtitleBase)
			}
		}

		resp.Videos = append(resp.Videos, videoBase)
	}
	return
}
