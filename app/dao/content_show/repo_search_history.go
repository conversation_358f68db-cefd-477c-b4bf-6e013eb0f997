package show

import (
	"github.com/gin-gonic/gin"
	"vlab/app/common/dbs"
)

func (e Entry) SearchHistoryCreate(ctx *gin.Context, model *ShowSearchHistory) (uint64, error) {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Create(&model).Error; err != nil {
		return 0, err
	}

	return model.ID, nil
}

/*
SELECT keyword,

	iso_639_1,
	COUNT(*) AS count

FROM vlab.content_show_search_history
WHERE created_at >= '2025-07-06 00:00:00'

	AND created_at < '2025-07-06 23:59:59'
	AND is_deleted = 0

GROUP BY keyword, iso_639_1
ORDER BY count DESC
LIMIT 50 OFFSET 0;
*/
func (e Entry) SearchHistoryGroup(ctx *gin.Context, filter *SearchHistoryFilter, page, limit int) (list GroupList, err error) {
	db := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&ShowSearchHistory{})

	db = db.Scopes(filter.fullScopes()...)
	if err = db.
		Select("keyword, iso_639_1 as lang, count(*) as count").
		Group("keyword, iso_639_1").Order("count DESC").Limit(limit).Offset((page - 1) * limit).
		Find(&list).Error; err != nil {
		return nil, err
	}

	return list, nil
}

type GroupListItem struct {
	Keyword string `json:"keyword"` // 关键词
	Lang    string `json:"lang"`    // 语言
	Count   int64  `json:"count"`   // 数量
}

type GroupList []GroupListItem
