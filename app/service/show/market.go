package show

import (
	"vlab/app/common/dbs"
	bannerDao "vlab/app/dao/admin_banner"
	i18nDao "vlab/app/dao/content_i18n"
	showDto "vlab/app/dto/show"
	"vlab/pkg/ecode"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm/clause"
)

// MarketBannerList 获取市场横幅列表 - 适配剧集数据展示
// 该方法处理剧集相关的横幅展示，支持版本审核控制和多语言国际化
// 复用剧集服务的公共验证和处理逻辑，保持架构一致性
func (e Entry) MarketBannerList(ctx *gin.Context, req *showDto.MarketBannerListReq) (res *showDto.MarketBannerListResp, err error) {
	res = &showDto.MarketBannerListResp{}
	res.List = make([]*showDto.MarketBannerListItem, 0)

	// 验证请求参数 - 复用现有的剧集列表验证逻辑
	reqCtx, err := e.validateShowListRequest(ctx)
	if err != nil {
		return nil, err
	}

	// 设置请求参数 - 与剧集服务保持一致的参数设置
	req.ChannelID = reqCtx.ChannelID
	req.VersionID = reqCtx.VersionID
	req.ISO_639_1 = reqCtx.ISO639_1

	// 构建横幅过滤器 - 适配剧集数据的横幅展示需求
	var (
		model  = bannerDao.Banner{}
		filter = &bannerDao.Filter{
			Status:   dbs.StatusEnable,
			Position: bannerDao.Position(req.Position),
			Sort: []clause.OrderByColumn{
				{
					Column: clause.Column{
						Name: dbs.GetColumnName(&model, "Sort")}, // Sort 正序
					Desc: false,
				},
				{
					Column: clause.Column{
						Name: dbs.GetColumnName(&model, "ID")}, // ID 倒序
					Desc: true,
				},
			},
			ChannelID: req.ChannelID,
		}
	)

	// 应用版本过滤逻辑 - 复用剧集服务的版本控制逻辑
	if t := reqCtx.VersionModel; t != nil {
		switch t.Status {
		case uint32(dbs.StatusEnable): // 若该版本已上线, 则需要正常返回, 不需要过滤
		case uint32(dbs.StatusDisable): // 若该版本已下线, 则需要返回空
			return res, nil
		case uint32(dbs.StatusAuditIng): // 若该版本在审核中, 则需要直接展示审核横幅
			filter.Status = dbs.StatusAuditIng
		}
	}

	// 查询横幅数据
	total, list, err := e.BannerRepo.DataPageList(ctx, filter, req.Page, req.Limit)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("MarketBannerList failed")
		return nil, ecode.SystemBusyErr
	}
	if total <= 0 || len(list) <= 0 {
		return res, nil
	}

	res.Total = total

	// 处理国际化数据 - 保持原始的多语言匹配逻辑
	var i18nMap map[string][]*i18nDao.I18n
	if len(list) > 0 {
		i18nKeys, err := e.I18nRepo.FindByFilter(ctx, &i18nDao.Filter{
			Keys:      list.GetKeys(),
			ISO_639_1: req.ISO_639_1,
		})
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("FindByFilter failed")
			return nil, ecode.SystemBusyErr
		}
		i18nMap = i18nKeys.GetI18nKey()
	}

	// 构建响应数据 - 适配剧集展示的横幅数据格式
	for _, item := range list {
		tmp := &showDto.MarketBannerListItem{
			AdminMarketBannerDetail: &showDto.AdminMarketBannerDetail{
				ID:       item.ID,
				Title:    item.Title,
				Cover:    item.GetFilePath(reqCtx.ChannelKeyInfo.OssImageHost),
				JumpType: item.JumpType,
				Jump:     item.Jump,
				Position: item.Position,
				Sort:     item.Sort,
			},
		}

		// 应用国际化标题 - 保持原始的语言匹配逻辑
		if v, ok := i18nMap[item.TitleKey]; ok {
			for _, n := range v {
				if n.ISO_639_1 == req.ISO_639_1 {
					tmp.Title = n.Value
					break
				}
			}
		}

		res.List = append(res.List, tmp)
	}

	return res, nil
}
